/* Main CSS File - مكتبة الإعجاز العلمي */

/* Root Variables */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #f8f9fa;
    --accent-color: #ffd700;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --white-color: #ffffff;
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--secondary-color);
    direction: rtl;
    text-align: right;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    font-family: 'Tajawal', sans-serif;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    background: var(--white-color);
    padding: 3rem;
    border-radius: var(--border-radius);
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: var(--shadow-heavy);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-title {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.loading-text {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.progress-container {
    margin: 1.5rem 0;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: var(--border-color);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    transition: width 0.3s ease;
}

.progress-text {
    margin-top: 0.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Header */
.header-section {
    background: var(--white-color);
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color) !important;
    text-decoration: none;
}

.navbar-brand i {
    margin-left: 0.5rem;
    font-size: 1.8rem;
}

.nav-link {
    font-weight: 500;
    color: var(--text-primary) !important;
    transition: var(--transition);
    padding: 0.5rem 1rem !important;
    border-radius: 6px;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color) !important;
    background-color: rgba(44, 90, 160, 0.1);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1e3c72 100%);
    color: var(--white-color);
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-title .text-primary {
    color: var(--accent-color) !important;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-buttons .btn {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    transition: var(--transition);
}

.hero-buttons .btn-primary {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--dark-color);
}

.hero-buttons .btn-primary:hover {
    background: #e6c200;
    border-color: #e6c200;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.hero-buttons .btn-outline-secondary {
    border-color: var(--white-color);
    color: var(--white-color);
}

.hero-buttons .btn-outline-secondary:hover {
    background: var(--white-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Hero Image */
.hero-image {
    position: relative;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-card {
    position: absolute;
    background: var(--white-color);
    color: var(--text-primary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    text-align: center;
    min-width: 150px;
    animation: float 6s ease-in-out infinite;
}

.floating-card:nth-child(1) {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.floating-card:nth-child(2) {
    bottom: 30%;
    left: 5%;
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    top: 50%;
    right: 50%;
    transform: translateX(50%);
    animation-delay: 4s;
}

.floating-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.floating-card h5 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
    color: var(--primary-color);
}

.floating-card p {
    font-size: 0.85rem;
    margin: 0;
    color: var(--text-secondary);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Search Section */
.search-section {
    padding: 5rem 0;
    background: var(--white-color);
}

.search-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    padding: 3rem;
    border: 1px solid var(--border-color);
}

.search-header {
    text-align: center;
    margin-bottom: 2rem;
}

.search-title {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.search-title i {
    margin-left: 0.5rem;
}

.search-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
}

.search-input-group {
    margin-bottom: 1.5rem;
}

.search-input {
    font-size: 1.1rem;
    padding: 1rem 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: 50px 0 0 50px;
    transition: var(--transition);
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.search-btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 0 50px 50px 0;
    background: var(--primary-color);
    border-color: var(--primary-color);
    transition: var(--transition);
}

.search-btn:hover {
    background: #1e3c72;
    border-color: #1e3c72;
    transform: translateY(-1px);
}

/* Quick Suggestions */
.quick-suggestions {
    margin-top: 2rem;
    text-align: center;
}

.quick-suggestions h6 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.suggestion-tag {
    background: var(--secondary-color);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.suggestion-tag:hover {
    background: var(--primary-color);
    color: var(--white-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

/* Results Section */
.results-section {
    padding: 3rem 0;
    background: var(--secondary-color);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.results-title {
    color: var(--primary-color);
    margin: 0;
}

.results-title i {
    margin-left: 0.5rem;
}

.results-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.results-actions .btn {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: var(--transition);
}

.results-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.results-content {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: 2rem;
    min-height: 400px;
}

/* Features Section */
.features-section {
    padding: 5rem 0;
    background: var(--white-color);
}

.section-header {
    margin-bottom: 3rem;
}

.section-title {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
}

.feature-card {
    background: var(--white-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    height: 100%;
    box-shadow: var(--shadow-light);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: var(--transition);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

.feature-icon i {
    font-size: 2rem;
    color: var(--white-color);
}

.feature-title {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* About Section */
.about-section {
    padding: 5rem 0;
    background: var(--secondary-color);
}

.about-title {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.about-description {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.about-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
}

.about-image {
    position: relative;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.about-card {
    background: var(--white-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    text-align: center;
    max-width: 300px;
}

.about-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.about-card h5 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.about-card p {
    color: var(--text-secondary);
    margin: 0;
}

/* Footer */
.footer-section {
    background: var(--dark-color);
    color: var(--white-color);
    padding: 3rem 0 1rem;
}

.footer-brand h5 {
    color: var(--white-color);
    margin-bottom: 1rem;
}

.footer-brand i {
    margin-left: 0.5rem;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.8);
}

.footer-title {
    color: var(--white-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--accent-color);
}

.footer-contact p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
}

.footer-contact i {
    margin-left: 0.5rem;
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--accent-color);
    color: var(--dark-color);
    transform: translateY(-2px);
}

.footer-divider {
    border-color: rgba(255, 255, 255, 0.2);
    margin: 2rem 0 1rem;
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.footer-settings {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.footer-settings .btn {
    font-size: 0.85rem;
    padding: 0.4rem 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .search-card {
        padding: 2rem 1.5rem;
    }
    
    .search-input,
    .search-btn {
        border-radius: var(--border-radius);
        margin-bottom: 0.5rem;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .results-header {
        flex-direction: column;
        text-align: center;
    }
    
    .about-stats {
        justify-content: center;
    }
    
    .floating-card {
        position: static;
        margin: 1rem;
        animation: none;
    }
    
    .hero-image {
        height: auto;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
    }
    
    .footer-settings {
        justify-content: center;
        margin-top: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .search-card {
        padding: 1.5rem 1rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .loading-content {
        padding: 2rem 1rem;
    }
    
    .suggestion-tags {
        justify-content: flex-start;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-hover {
    transition: var(--transition);
}

.shadow-hover:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.btn-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    border: none;
    color: var(--white-color);
}

.btn-gradient:hover {
    background: linear-gradient(135deg, #1e3c72, #0d6efd);
    color: var(--white-color);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1e3c72;
}

/* Selection Styling */
::selection {
    background: var(--primary-color);
    color: var(--white-color);
}

::-moz-selection {
    background: var(--primary-color);
    color: var(--white-color);
}
