// AI Integration Module - مكتبة الإعجاز العلمي

class AIIntegration {
    constructor() {
        this.isProcessing = false;
        this.currentRequest = null;
        this.progressCallback = null;
        this.isPaused = false;
        this.isStopped = false;
    }

    // إعداد callback للتقدم
    setProgressCallback(callback) {
        this.progressCallback = callback;
    }

    // تحديث التقدم
    updateProgress(percentage, message) {
        if (this.progressCallback) {
            this.progressCallback(percentage, message);
        }
    }

    // إيقاف مؤقت للعملية
    pause() {
        this.isPaused = true;
        this.updateProgress(null, 'تم إيقاف العملية مؤقتاً...');
    }

    // استئناف العملية
    resume() {
        this.isPaused = false;
        this.updateProgress(null, 'تم استئناف العملية...');
    }

    // إيقاف العملية نهائياً
    stop() {
        this.isStopped = true;
        this.isPaused = false;
        if (this.currentRequest) {
            this.currentRequest.abort();
        }
        this.updateProgress(100, 'تم إيقاف العملية');
    }

    // إعادة تعيين حالة العملية
    reset() {
        this.isProcessing = false;
        this.currentRequest = null;
        this.isPaused = false;
        this.isStopped = false;
    }

    // انتظار في حالة الإيقاف المؤقت
    async waitIfPaused() {
        while (this.isPaused && !this.isStopped) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    // إرسال طلب إلى Gemini API
    async sendGeminiRequest(prompt, retryCount = 0) {
        if (this.isStopped) {
            throw new Error('تم إيقاف العملية');
        }

        await this.waitIfPaused();
        await apiManager.enforceRateLimit();

        const apiKey = apiManager.getNextAvailableKey();
        const url = `${GEMINI_API_URL}?key=${apiKey}`;

        const requestBody = {
            contents: [{
                parts: [{ text: prompt }]
            }],
            generationConfig: {
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 4000,
            },
            safetySettings: [
                {
                    category: "HARM_CATEGORY_HARASSMENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_HATE_SPEECH",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
        };

        try {
            const controller = new AbortController();
            this.currentRequest = controller;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                
                // التعامل مع أخطاء محددة
                if (response.status === 429) {
                    // تجاوز حد الطلبات
                    if (retryCount < CONTENT_SETTINGS.maxRetries) {
                        this.updateProgress(null, `تجاوز حد الطلبات، إعادة المحاولة ${retryCount + 1}...`);
                        await new Promise(resolve => setTimeout(resolve, CONTENT_SETTINGS.retryDelay * (retryCount + 1)));
                        return this.sendGeminiRequest(prompt, retryCount + 1);
                    }
                    throw new Error('تم تجاوز حد الطلبات المسموح به');
                }
                
                if (response.status === 400) {
                    throw new Error('خطأ في تنسيق الطلب أو المحتوى غير مناسب');
                }
                
                if (response.status === 403) {
                    throw new Error('مفتاح API غير صالح أو منتهي الصلاحية');
                }

                throw new Error(`خطأ في API: ${response.status} - ${errorData.error?.message || 'خطأ غير معروف'}`);
            }

            const data = await response.json();
            
            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                throw new Error('استجابة غير صالحة من API');
            }

            apiManager.recordKeyUsage();
            this.currentRequest = null;

            return data.candidates[0].content.parts[0].text;

        } catch (error) {
            this.currentRequest = null;
            
            if (error.name === 'AbortError') {
                throw new Error('تم إلغاء الطلب');
            }
            
            // إعادة المحاولة في حالة أخطاء الشبكة
            if (retryCount < CONTENT_SETTINGS.maxRetries && 
                (error.message.includes('fetch') || error.message.includes('network'))) {
                this.updateProgress(null, `خطأ في الشبكة، إعادة المحاولة ${retryCount + 1}...`);
                await new Promise(resolve => setTimeout(resolve, CONTENT_SETTINGS.retryDelay));
                return this.sendGeminiRequest(prompt, retryCount + 1);
            }
            
            throw error;
        }
    }

    // معالجة المحتوى الكبير بتقسيمه
    async processLargeContent(topic, settings) {
        if (this.isProcessing) {
            throw new Error('يتم معالجة طلب آخر حالياً');
        }

        this.isProcessing = true;
        this.reset();

        try {
            this.updateProgress(5, 'بدء تحليل الموضوع...');

            // إنشاء الطلب الأساسي
            const basePrompt = this.createBasePrompt(topic, settings);
            
            // تقسيم المعالجة إلى مراحل
            const phases = this.createProcessingPhases(topic, settings);
            const results = [];
            
            for (let i = 0; i < phases.length; i++) {
                if (this.isStopped) break;
                
                await this.waitIfPaused();
                
                const phase = phases[i];
                const progress = 10 + (i / phases.length) * 80;
                
                this.updateProgress(progress, `معالجة ${phase.title}...`);
                
                try {
                    const result = await this.sendGeminiRequest(phase.prompt);
                    results.push({
                        title: phase.title,
                        content: result,
                        type: phase.type
                    });
                    
                    // تأخير قصير بين الطلبات
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                } catch (error) {
                    console.error(`خطأ في معالجة ${phase.title}:`, error);
                    results.push({
                        title: phase.title,
                        content: `خطأ في معالجة هذا القسم: ${error.message}`,
                        type: 'error'
                    });
                }
            }

            if (this.isStopped) {
                throw new Error('تم إيقاف العملية من قبل المستخدم');
            }

            this.updateProgress(95, 'تجميع النتائج النهائية...');
            
            // تجميع النتائج
            const finalResult = this.combineResults(results, topic, settings);
            
            this.updateProgress(100, 'تم إكمال التحليل بنجاح');
            
            return finalResult;

        } catch (error) {
            this.updateProgress(100, `خطأ: ${error.message}`);
            throw error;
        } finally {
            this.isProcessing = false;
        }
    }

    // إنشاء الطلب الأساسي
    createBasePrompt(topic, settings) {
        let prompt = SYSTEM_PROMPTS.main + '\n\n';
        
        prompt += `الموضوع المطلوب تحليله: ${topic}\n\n`;
        
        // إضافة الإعدادات
        if (settings.detailLevel) {
            const levelMap = {
                'basic': 'أساسي - ركز على النقاط الرئيسية فقط',
                'intermediate': 'متوسط - قدم تفاصيل معتدلة مع أمثلة',
                'advanced': 'متقدم - تحليل مفصل مع مراجع علمية',
                'expert': 'خبير - تحليل شامل ومعمق مع مناقشة الجوانب المعقدة'
            };
            prompt += `مستوى التفصيل المطلوب: ${levelMap[settings.detailLevel]}\n`;
        }
        
        if (settings.sourceType && settings.sourceType !== 'all') {
            const sourceMap = {
                'quran': 'ركز على الآيات القرآنية فقط',
                'sunnah': 'ركز على الأحاديث النبوية فقط',
                'both': 'اشمل القرآن والسنة معاً'
            };
            prompt += `نوع المصادر: ${sourceMap[settings.sourceType]}\n`;
        }
        
        return prompt;
    }

    // إنشاء مراحل المعالجة
    createProcessingPhases(topic, settings) {
        const phases = [];
        
        // المرحلة الأولى: التحليل الأساسي
        phases.push({
            title: 'التحليل الأساسي والآيات القرآنية',
            type: 'analysis',
            prompt: `${this.createBasePrompt(topic, settings)}

قم بتحليل الموضوع وتقديم:
1. مقدمة موجزة عن المجال العلمي
2. الآيات القرآنية ذات الصلة مع أرقامها وأسماء السور
3. تفسير مبسط لكل آية في السياق العلمي
4. الحقائق العلمية الأساسية المرتبطة

اجعل الإجابة منظمة ومفصلة.`
        });

        // المرحلة الثانية: الأحاديث والسنة
        if (settings.sourceType !== 'quran') {
            phases.push({
                title: 'الأحاديث النبوية والسنة',
                type: 'hadith',
                prompt: `${this.createBasePrompt(topic, settings)}

ركز على:
1. الأحاديث النبوية الصحيحة المرتبطة بـ ${topic}
2. المواقف من السيرة النبوية ذات الصلة
3. تخريج الأحاديث ودرجة صحتها
4. الشرح العلمي للأحاديث
5. أوجه الإعجاز في السنة النبوية

تأكد من صحة الأحاديث المذكورة.`
            });
        }

        // المرحلة الثالثة: التحليل العلمي المتقدم
        phases.push({
            title: 'التحليل العلمي والاكتشافات الحديثة',
            type: 'scientific',
            prompt: `${this.createBasePrompt(topic, settings)}

قدم تحليلاً علمياً متقدماً يشمل:
1. أحدث الاكتشافات العلمية في مجال ${topic}
2. مقارنة بين ما ذكر في النصوص الشرعية والعلم الحديث
3. أوجه السبق العلمي في القرآن والسنة
4. الدقة العلمية في التعبيرات القرآنية والنبوية
5. شهادات علماء غير مسلمين إن وجدت

استخدم مصطلحات علمية دقيقة.`
            });

        // المرحلة الرابعة: الاعتراضات والردود
        phases.push({
            title: 'الاعتراضات والردود العلمية',
            type: 'objections',
            prompt: `${this.createBasePrompt(topic, settings)}

ناقش:
1. أهم الاعتراضات على الإعجاز العلمي في ${topic}
2. الرد العلمي المفصل على كل اعتراض
3. لماذا التفسير العلمي الحديث أقوى من التأويلات القديمة
4. الفرق بين الحقائق العلمية المؤكدة والنظريات القابلة للتغيير
5. ضوابط الإعجاز العلمي الصحيح

كن موضوعياً ومنطقياً في الردود.`
            });

        // المرحلة الخامسة: الجداول والإحصائيات (إذا كانت مطلوبة)
        if (settings.includeTables) {
            phases.push({
                title: 'الجداول والمقارنات',
                type: 'tables',
                prompt: `${this.createBasePrompt(topic, settings)}

أنشئ جداول منظمة تتضمن:
1. جدول مقارنة بين النصوص الشرعية والحقائق العلمية
2. جدول زمني للاكتشافات العلمية مقارنة بتاريخ النزول
3. جدول المصطلحات العلمية في القرآن والسنة
4. إحصائيات مهمة حول الموضوع

استخدم تنسيق HTML للجداول مع CSS classes مناسبة.`
            });
        }

        // المرحلة السادسة: الأسئلة الشائعة (إذا كانت مطلوبة)
        if (settings.includeFAQ) {
            phases.push({
                title: 'الأسئلة الشائعة',
                type: 'faq',
                prompt: `${this.createBasePrompt(topic, settings)}

أنشئ قسم أسئلة شائعة يتضمن:
1. 10-15 سؤال شائع حول الإعجاز العلمي في ${topic}
2. إجابات مفصلة وعلمية لكل سؤال
3. أسئلة حول الشبهات والردود عليها
4. أسئلة تقنية للمتخصصين

رتب الأسئلة من الأساسي إلى المتقدم.`
            });
        }

        return phases;
    }

    // تجميع النتائج النهائية
    combineResults(results, topic, settings) {
        const timestamp = Utils.formatDate();
        const searchId = Utils.generateId();
        
        let combinedContent = `
# الإعجاز العلمي في ${topic}

**تاريخ البحث:** ${timestamp}  
**معرف البحث:** ${searchId}

---

`;

        // إضافة المحتوى من كل مرحلة
        results.forEach((result, index) => {
            if (result.type !== 'error') {
                combinedContent += `## ${result.title}\n\n`;
                combinedContent += `${result.content}\n\n`;
                combinedContent += `---\n\n`;
            }
        });

        // إضافة المصادر والمراجع
        combinedContent += `## المصادر والمراجع

### المصادر الشرعية:
- القرآن الكريم
- صحيح البخاري
- صحيح مسلم
- سنن أبي داود
- سنن الترمذي
- سنن النسائي
- سنن ابن ماجه

### المصادر العلمية:
- المجلات العلمية المحكمة
- الموسوعات العلمية المعتمدة
- أبحاث الجامعات والمؤسسات العلمية
- تقارير المنظمات العلمية الدولية

---

**تنبيه:** هذا التحليل مبني على المعرفة المتاحة حتى تاريخ إنشائه. يُنصح بمراجعة المصادر الأصلية والتحقق من المعلومات.
`;

        return {
            content: Utils.cleanText(combinedContent),
            topic: topic,
            timestamp: timestamp,
            searchId: searchId,
            settings: settings,
            results: results,
            wordCount: combinedContent.split(' ').length,
            sections: results.length
        };
    }

    // الحصول على إحصائيات API
    getAPIStats() {
        return apiManager.getUsageStats();
    }
}

// إنشاء مثيل من فئة التكامل مع الذكاء الاصطناعي
const aiIntegration = new AIIntegration();

// تصدير للاستخدام في الملفات الأخرى
window.AIIntegration = AIIntegration;
window.aiIntegration = aiIntegration;

console.log('تم تحميل وحدة التكامل مع الذكاء الاصطناعي بنجاح');
