// AI Integration Module - مكتبة الإعجاز العلمي

class AIIntegration {
    constructor() {
        this.isProcessing = false;
        this.currentRequest = null;
        this.progressCallback = null;
        this.isPaused = false;
        this.isStopped = false;
    }

    // إعداد callback للتقدم
    setProgressCallback(callback) {
        this.progressCallback = callback;
    }

    // تحديث التقدم
    updateProgress(percentage, message) {
        if (this.progressCallback) {
            this.progressCallback(percentage, message);
        }
    }

    // إيقاف مؤقت للعملية
    pause() {
        this.isPaused = true;
        this.updateProgress(null, 'تم إيقاف العملية مؤقتاً...');
    }

    // استئناف العملية
    resume() {
        this.isPaused = false;
        this.updateProgress(null, 'تم استئناف العملية...');
    }

    // إيقاف العملية نهائياً
    stop() {
        this.isStopped = true;
        this.isPaused = false;
        if (this.currentRequest) {
            this.currentRequest.abort();
        }
        this.updateProgress(100, 'تم إيقاف العملية');
    }

    // إعادة تعيين حالة العملية
    reset() {
        this.isProcessing = false;
        this.currentRequest = null;
        this.isPaused = false;
        this.isStopped = false;
    }

    // انتظار في حالة الإيقاف المؤقت
    async waitIfPaused() {
        while (this.isPaused && !this.isStopped) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    // إرسال طلب إلى Gemini API
    async sendGeminiRequest(prompt, retryCount = 0) {
        if (this.isStopped) {
            throw new Error('تم إيقاف العملية');
        }

        await this.waitIfPaused();
        await apiManager.enforceRateLimit();

        const apiKey = apiManager.getNextAvailableKey();

        // التحقق من وجود مفتاح API صالح
        if (!apiKey) {
            throw new Error('لا توجد مفاتيح API متاحة');
        }

        const url = `${GEMINI_API_URL}?key=${apiKey}`;

        const requestBody = {
            contents: [{
                parts: [{ text: prompt }]
            }],
            generationConfig: {
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 4000,
            },
            safetySettings: [
                {
                    category: "HARM_CATEGORY_HARASSMENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_HATE_SPEECH",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
        };

        try {
            console.log(`إرسال طلب إلى Gemini API (المحاولة ${retryCount + 1})`);

            const controller = new AbortController();
            this.currentRequest = controller;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            console.log(`استجابة API: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('خطأ في API:', errorData);

                // التعامل مع أخطاء محددة
                if (response.status === 429) {
                    // تجاوز حد الطلبات
                    if (retryCount < CONTENT_SETTINGS.maxRetries) {
                        this.updateProgress(null, `تجاوز حد الطلبات، إعادة المحاولة ${retryCount + 1}...`);
                        await new Promise(resolve => setTimeout(resolve, CONTENT_SETTINGS.retryDelay * (retryCount + 1)));
                        return this.sendGeminiRequest(prompt, retryCount + 1);
                    }
                    throw new Error('تم تجاوز حد الطلبات المسموح به');
                }

                if (response.status === 400) {
                    throw new Error('خطأ في تنسيق الطلب أو المحتوى غير مناسب');
                }

                if (response.status === 403) {
                    throw new Error('مفتاح API غير صالح أو منتهي الصلاحية');
                }

                throw new Error(`خطأ في API: ${response.status} - ${errorData.error?.message || 'خطأ غير معروف'}`);
            }

            const data = await response.json();
            console.log('تم استلام البيانات من API بنجاح');

            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                console.error('استجابة غير صالحة:', data);
                throw new Error('استجابة غير صالحة من API');
            }

            apiManager.recordKeyUsage();
            this.currentRequest = null;

            const result = data.candidates[0].content.parts[0].text;
            console.log(`تم الحصول على نتيجة بطول ${result.length} حرف`);

            return result;

        } catch (error) {
            this.currentRequest = null;
            console.error('خطأ في sendGeminiRequest:', error);

            if (error.name === 'AbortError') {
                throw new Error('تم إلغاء الطلب');
            }

            // إعادة المحاولة في حالة أخطاء الشبكة
            if (retryCount < CONTENT_SETTINGS.maxRetries &&
                (error.message.includes('fetch') || error.message.includes('network') || error.message.includes('Failed to fetch'))) {
                this.updateProgress(null, `خطأ في الشبكة، إعادة المحاولة ${retryCount + 1}...`);
                await new Promise(resolve => setTimeout(resolve, CONTENT_SETTINGS.retryDelay));
                return this.sendGeminiRequest(prompt, retryCount + 1);
            }

            throw error;
        }
    }

    // معالجة المحتوى الكبير بتقسيمه
    async processLargeContent(topic, settings) {
        if (this.isProcessing) {
            throw new Error('يتم معالجة طلب آخر حالياً');
        }

        this.isProcessing = true;
        this.reset();

        try {
            this.updateProgress(5, 'بدء تحليل الموضوع...');

            // إنشاء الطلب الأساسي
            const basePrompt = this.createBasePrompt(topic, settings);

            // تقسيم المعالجة إلى مراحل
            const phases = this.createProcessingPhases(topic, settings);
            const results = [];

            for (let i = 0; i < phases.length; i++) {
                if (this.isStopped) break;

                await this.waitIfPaused();

                const phase = phases[i];
                const progress = 10 + (i / phases.length) * 80;

                this.updateProgress(progress, `معالجة ${phase.title}...`);

                try {
                    const result = await this.sendGeminiRequest(phase.prompt);
                    if (result && result.trim()) {
                        results.push({
                            title: phase.title,
                            content: result,
                            type: phase.type
                        });
                    } else {
                        console.warn(`نتيجة فارغة من ${phase.title}`);
                        results.push({
                            title: phase.title,
                            content: this.generateFallbackContent(topic, phase.type),
                            type: phase.type
                        });
                    }

                    // تأخير قصير بين الطلبات
                    await new Promise(resolve => setTimeout(resolve, 1000));

                } catch (error) {
                    console.error(`خطأ في معالجة ${phase.title}:`, error);
                    results.push({
                        title: phase.title,
                        content: this.generateFallbackContent(topic, phase.type),
                        type: 'fallback'
                    });
                }
            }

            if (this.isStopped) {
                throw new Error('تم إيقاف العملية من قبل المستخدم');
            }

            // التأكد من وجود نتائج
            if (results.length === 0) {
                results.push({
                    title: 'المحتوى الأساسي',
                    content: this.generateFallbackContent(topic, 'basic'),
                    type: 'fallback'
                });
            }

            this.updateProgress(95, 'تجميع النتائج النهائية...');

            // تجميع النتائج
            const finalResult = this.combineResults(results, topic, settings);

            this.updateProgress(100, 'تم إكمال التحليل بنجاح');

            return finalResult;

        } catch (error) {
            console.error('خطأ في processLargeContent:', error);
            this.updateProgress(100, `خطأ: ${error.message}`);

            // إنشاء نتيجة احتياطية في حالة الفشل الكامل
            const fallbackResult = {
                content: this.generateFallbackContent(topic, 'complete'),
                topic: topic,
                timestamp: Utils.formatDate(),
                searchId: Utils.generateId(),
                settings: settings,
                results: [{
                    title: 'محتوى احتياطي',
                    content: this.generateFallbackContent(topic, 'complete'),
                    type: 'fallback'
                }],
                wordCount: 500,
                sections: 1
            };

            return fallbackResult;
        } finally {
            this.isProcessing = false;
        }
    }

    // إنشاء الطلب الأساسي
    createBasePrompt(topic, settings) {
        let prompt = SYSTEM_PROMPTS.main + '\n\n';
        
        prompt += `الموضوع المطلوب تحليله: ${topic}\n\n`;
        
        // إضافة الإعدادات
        if (settings.detailLevel) {
            const levelMap = {
                'basic': 'أساسي - ركز على النقاط الرئيسية فقط',
                'intermediate': 'متوسط - قدم تفاصيل معتدلة مع أمثلة',
                'advanced': 'متقدم - تحليل مفصل مع مراجع علمية',
                'expert': 'خبير - تحليل شامل ومعمق مع مناقشة الجوانب المعقدة'
            };
            prompt += `مستوى التفصيل المطلوب: ${levelMap[settings.detailLevel]}\n`;
        }
        
        if (settings.sourceType && settings.sourceType !== 'all') {
            const sourceMap = {
                'quran': 'ركز على الآيات القرآنية فقط',
                'sunnah': 'ركز على الأحاديث النبوية فقط',
                'both': 'اشمل القرآن والسنة معاً'
            };
            prompt += `نوع المصادر: ${sourceMap[settings.sourceType]}\n`;
        }
        
        return prompt;
    }

    // إنشاء مراحل المعالجة
    createProcessingPhases(topic, settings) {
        const phases = [];
        
        // المرحلة الأولى: التحليل الأساسي
        phases.push({
            title: 'التحليل الأساسي والآيات القرآنية',
            type: 'analysis',
            prompt: `${this.createBasePrompt(topic, settings)}

قم بتحليل الموضوع وتقديم:
1. مقدمة موجزة عن المجال العلمي
2. الآيات القرآنية ذات الصلة مع أرقامها وأسماء السور
3. تفسير مبسط لكل آية في السياق العلمي
4. الحقائق العلمية الأساسية المرتبطة

اجعل الإجابة منظمة ومفصلة.`
        });

        // المرحلة الثانية: الأحاديث والسنة
        if (settings.sourceType !== 'quran') {
            phases.push({
                title: 'الأحاديث النبوية والسنة',
                type: 'hadith',
                prompt: `${this.createBasePrompt(topic, settings)}

ركز على:
1. الأحاديث النبوية الصحيحة المرتبطة بـ ${topic}
2. المواقف من السيرة النبوية ذات الصلة
3. تخريج الأحاديث ودرجة صحتها
4. الشرح العلمي للأحاديث
5. أوجه الإعجاز في السنة النبوية

تأكد من صحة الأحاديث المذكورة.`
            });
        }

        // المرحلة الثالثة: التحليل العلمي المتقدم
        phases.push({
            title: 'التحليل العلمي والاكتشافات الحديثة',
            type: 'scientific',
            prompt: `${this.createBasePrompt(topic, settings)}

قدم تحليلاً علمياً متقدماً يشمل:
1. أحدث الاكتشافات العلمية في مجال ${topic}
2. مقارنة بين ما ذكر في النصوص الشرعية والعلم الحديث
3. أوجه السبق العلمي في القرآن والسنة
4. الدقة العلمية في التعبيرات القرآنية والنبوية
5. شهادات علماء غير مسلمين إن وجدت

استخدم مصطلحات علمية دقيقة.`
            });

        // المرحلة الرابعة: الاعتراضات والردود
        phases.push({
            title: 'الاعتراضات والردود العلمية',
            type: 'objections',
            prompt: `${this.createBasePrompt(topic, settings)}

ناقش:
1. أهم الاعتراضات على الإعجاز العلمي في ${topic}
2. الرد العلمي المفصل على كل اعتراض
3. لماذا التفسير العلمي الحديث أقوى من التأويلات القديمة
4. الفرق بين الحقائق العلمية المؤكدة والنظريات القابلة للتغيير
5. ضوابط الإعجاز العلمي الصحيح

كن موضوعياً ومنطقياً في الردود.`
            });

        // المرحلة الخامسة: الجداول والإحصائيات (إذا كانت مطلوبة)
        if (settings.includeTables) {
            phases.push({
                title: 'الجداول والمقارنات',
                type: 'tables',
                prompt: `${this.createBasePrompt(topic, settings)}

أنشئ جداول منظمة تتضمن:
1. جدول مقارنة بين النصوص الشرعية والحقائق العلمية
2. جدول زمني للاكتشافات العلمية مقارنة بتاريخ النزول
3. جدول المصطلحات العلمية في القرآن والسنة
4. إحصائيات مهمة حول الموضوع

استخدم تنسيق HTML للجداول مع CSS classes مناسبة.`
            });
        }

        // المرحلة السادسة: الأسئلة الشائعة (إذا كانت مطلوبة)
        if (settings.includeFAQ) {
            phases.push({
                title: 'الأسئلة الشائعة',
                type: 'faq',
                prompt: `${this.createBasePrompt(topic, settings)}

أنشئ قسم أسئلة شائعة يتضمن:
1. 10-15 سؤال شائع حول الإعجاز العلمي في ${topic}
2. إجابات مفصلة وعلمية لكل سؤال
3. أسئلة حول الشبهات والردود عليها
4. أسئلة تقنية للمتخصصين

رتب الأسئلة من الأساسي إلى المتقدم.`
            });
        }

        return phases;
    }

    // تجميع النتائج النهائية
    combineResults(results, topic, settings) {
        const timestamp = Utils.formatDate();
        const searchId = Utils.generateId();

        console.log(`تجميع النتائج للموضوع: ${topic}`);
        console.log(`عدد النتائج: ${results.length}`);

        let combinedContent = `# الإعجاز العلمي في ${topic}

**تاريخ البحث:** ${timestamp}
**معرف البحث:** ${searchId}

---

`;

        // إضافة المحتوى من كل مرحلة
        let validResults = 0;
        results.forEach((result, index) => {
            if (result.type !== 'error' && result.content && result.content.trim()) {
                combinedContent += `## ${result.title}\n\n`;
                combinedContent += `${result.content}\n\n`;
                combinedContent += `---\n\n`;
                validResults++;
                console.log(`تم إضافة القسم: ${result.title}`);
            } else {
                console.warn(`تم تخطي القسم: ${result.title} (${result.type})`);
            }
        });

        // إذا لم تكن هناك نتائج صالحة، أضف محتوى احتياطي
        if (validResults === 0) {
            console.warn('لا توجد نتائج صالحة، إضافة محتوى احتياطي');
            combinedContent += this.generateFallbackContent(topic, 'complete');
        }

        // إضافة المصادر والمراجع
        combinedContent += `

## المصادر والمراجع

### المصادر الشرعية:
- القرآن الكريم
- صحيح البخاري
- صحيح مسلم
- سنن أبي داود
- سنن الترمذي
- سنن النسائي
- سنن ابن ماجه

### المصادر العلمية:
- المجلات العلمية المحكمة
- الموسوعات العلمية المعتمدة
- أبحاث الجامعات والمؤسسات العلمية
- تقارير المنظمات العلمية الدولية

---

**تنبيه:** هذا التحليل مبني على المعرفة المتاحة حتى تاريخ إنشائه. يُنصح بمراجعة المصادر الأصلية والتحقق من المعلومات.
`;

        const finalResult = {
            content: Utils.cleanText(combinedContent),
            topic: topic,
            timestamp: timestamp,
            searchId: searchId,
            settings: settings,
            results: results,
            wordCount: combinedContent.split(' ').length,
            sections: validResults || 1
        };

        console.log(`تم تجميع النتائج: ${finalResult.wordCount} كلمة، ${finalResult.sections} قسم`);
        return finalResult;
    }

    // إنتاج محتوى احتياطي في حالة فشل API
    generateFallbackContent(topic, type) {
        const fallbackContent = {
            'basic': `
## مقدمة عن ${topic}

يُعتبر مجال ${topic} من المجالات العلمية المهمة التي شهدت تطوراً كبيراً في العصر الحديث. وقد أشار القرآن الكريم والسنة النبوية إلى العديد من الحقائق المتعلقة بهذا المجال قبل اكتشافها بقرون عديدة.

## الآيات القرآنية ذات الصلة

هناك العديد من الآيات القرآنية التي تشير إلى جوانب مختلفة من ${topic}، والتي تُظهر الإعجاز العلمي في القرآن الكريم.

## الأحاديث النبوية

كما وردت أحاديث نبوية شريفة تتعلق بـ${topic}، مما يدل على السبق العلمي في الإسلام.

## الحقائق العلمية الحديثة

الاكتشافات العلمية الحديثة في مجال ${topic} تؤكد ما جاء في النصوص الشرعية، مما يُظهر الإعجاز العلمي بوضوح.

## خلاصة

إن دراسة الإعجاز العلمي في ${topic} تُظهر التطابق الرائع بين النصوص الشرعية والحقائق العلمية المعاصرة.
            `,

            'analysis': `
## تحليل الإعجاز العلمي في ${topic}

### النصوص الشرعية
تحتوي النصوص الشرعية على إشارات واضحة إلى جوانب مختلفة من ${topic}.

### التحليل العلمي
الدراسات العلمية الحديثة تؤكد صحة ما جاء في النصوص الشرعية.

### أوجه الإعجاز
- السبق الزمني في الإشارة إلى الحقائق العلمية
- الدقة في التعبير العلمي
- التطابق مع الاكتشافات الحديثة
            `,

            'complete': `
# الإعجاز العلمي في ${topic}

## مقدمة
يُعد موضوع ${topic} من الموضوعات العلمية المهمة التي تُظهر الإعجاز العلمي في القرآن الكريم والسنة النبوية الشريفة.

## الآيات القرآنية
هناك العديد من الآيات القرآنية التي تشير إلى جوانب مختلفة من ${topic}، وتُظهر السبق العلمي للقرآن الكريم.

## الأحاديث النبوية
وردت أحاديث نبوية شريفة تتعلق بـ${topic}، مما يدل على الإعجاز العلمي في السنة النبوية.

## الحقائق العلمية الحديثة
الاكتشافات العلمية المعاصرة في مجال ${topic} تؤكد ما جاء في النصوص الشرعية.

## التحليل والمقارنة
عند مقارنة النصوص الشرعية مع الحقائق العلمية الحديثة، نجد تطابقاً مذهلاً يُظهر الإعجاز العلمي.

## الخلاصة
إن دراسة الإعجاز العلمي في ${topic} تُثبت أن القرآن الكريم والسنة النبوية مصدران موثوقان للمعرفة العلمية.

---

**ملاحظة:** هذا محتوى أساسي. للحصول على تحليل أكثر تفصيلاً، يُرجى المحاولة مرة أخرى أو التحقق من اتصال الإنترنت.
            `
        };

        return fallbackContent[type] || fallbackContent['basic'];
    }

    // الحصول على إحصائيات API
    getAPIStats() {
        return apiManager.getUsageStats();
    }
}

// إنشاء مثيل من فئة التكامل مع الذكاء الاصطناعي
const aiIntegration = new AIIntegration();

// تصدير للاستخدام في الملفات الأخرى
window.AIIntegration = AIIntegration;
window.aiIntegration = aiIntegration;

console.log('تم تحميل وحدة التكامل مع الذكاء الاصطناعي بنجاح');
