/* Components CSS - مكتبة الإعجاز العلمي */

/* Verse Components */
.verse-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-right: 4px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.verse-container:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.verse-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.verse-title {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.verse-title i {
    margin-left: 0.5rem;
    font-size: 1.2rem;
}

.verse-content {
    line-height: 1.8;
}

.verse-text {
    background: var(--white-color);
    border: 1px solid #e3f2fd;
    border-radius: 8px;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--primary-color);
    text-align: center;
    margin: 1rem 0;
    position: relative;
}

.verse-text::before {
    content: '"';
    position: absolute;
    top: -10px;
    right: 10px;
    font-size: 2rem;
    color: var(--primary-color);
    background: var(--white-color);
    padding: 0 5px;
}

.verse-text::after {
    content: '"';
    position: absolute;
    bottom: -10px;
    left: 10px;
    font-size: 2rem;
    color: var(--primary-color);
    background: var(--white-color);
    padding: 0 5px;
}

.verse-interpretation h5 {
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.verse-interpretation p {
    color: var(--text-secondary);
    line-height: 1.7;
}

.verse-highlight {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-right: 3px solid var(--primary-color);
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 6px;
}

.verse-highlight i {
    margin-left: 0.5rem;
}

.verse-highlight .verse-text {
    font-weight: 600;
    color: var(--primary-color);
}

/* Hadith Components */
.hadith-container {
    background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
    border: 1px solid #c8e6c9;
    border-right: 4px solid var(--success-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.hadith-container:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.hadith-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #c8e6c9;
}

.hadith-title {
    color: var(--success-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.hadith-title i {
    margin-left: 0.5rem;
    font-size: 1.2rem;
}

.hadith-grade {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
}

.hadith-content {
    line-height: 1.8;
}

.hadith-text {
    background: var(--white-color);
    border: 1px solid #c8e6c9;
    border-radius: 8px;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--success-color);
    text-align: center;
    margin: 1rem 0;
    position: relative;
}

.hadith-text::before {
    content: '"';
    position: absolute;
    top: -10px;
    right: 10px;
    font-size: 2rem;
    color: var(--success-color);
    background: var(--white-color);
    padding: 0 5px;
}

.hadith-text::after {
    content: '"';
    position: absolute;
    bottom: -10px;
    left: 10px;
    font-size: 2rem;
    color: var(--success-color);
    background: var(--white-color);
    padding: 0 5px;
}

.hadith-narrator {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-style: italic;
    margin: 0.5rem 0;
}

.hadith-interpretation h5 {
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.hadith-interpretation p {
    color: var(--text-secondary);
    line-height: 1.7;
}

.hadith-highlight {
    background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
    border-right: 3px solid var(--success-color);
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 6px;
}

.hadith-highlight i {
    margin-left: 0.5rem;
}

.hadith-highlight .hadith-text {
    font-weight: 600;
    color: var(--success-color);
}

/* Scientific Fact Components */
.scientific-fact {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border: 1px solid #81c784;
    border-right: 4px solid var(--info-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.scientific-fact:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.fact-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #81c784;
}

.fact-title {
    color: var(--info-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.fact-title i {
    margin-left: 0.5rem;
    font-size: 1.2rem;
}

.fact-year {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
}

.fact-content p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 0.5rem;
}

.fact-source {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* Comparison Components */
.comparison-container {
    background: var(--white-color);
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: var(--shadow-light);
}

.comparison-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--warning-color);
}

.comparison-header h4 {
    color: var(--warning-color);
    font-weight: 600;
    margin: 0;
}

.comparison-header i {
    margin-left: 0.5rem;
    font-size: 1.3rem;
}

.comparison-content .row {
    margin-bottom: 1rem;
}

.comparison-item {
    background: var(--secondary-color);
    border-radius: 8px;
    padding: 1rem;
    height: 100%;
    transition: var(--transition);
}

.comparison-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.comparison-item.quran {
    border-right: 3px solid var(--primary-color);
}

.comparison-item.science {
    border-right: 3px solid var(--info-color);
}

.comparison-item h5 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.comparison-item h5 i {
    margin-left: 0.5rem;
}

.comparison-item.quran h5 {
    color: var(--primary-color);
}

.comparison-item.science h5 {
    color: var(--info-color);
}

.comparison-item p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

.comparison-analysis {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.comparison-analysis h5 {
    color: var(--warning-color);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.comparison-analysis p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin: 0;
}

/* Table Components */
.table-container {
    background: var(--white-color);
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

.table-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--secondary-color);
}

.table-header h4 {
    color: var(--text-primary);
    font-weight: 600;
    margin: 0;
}

.table-header i {
    margin-left: 0.5rem;
    color: var(--secondary-color);
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.table {
    margin: 0;
    font-size: 0.95rem;
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1e3c72 100%);
    color: var(--white-color);
    font-weight: 600;
    border: none;
    padding: 1rem 0.75rem;
    text-align: center;
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-color: #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(44, 90, 160, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(44, 90, 160, 0.1);
    transform: scale(1.01);
    transition: var(--transition);
}

/* FAQ Components */
.faq-container {
    background: var(--white-color);
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: var(--shadow-light);
}

.faq-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--info-color);
}

.faq-header h3 {
    color: var(--info-color);
    font-weight: 600;
    margin: 0;
}

.faq-header i {
    margin-left: 0.5rem;
    font-size: 1.4rem;
}

.accordion-item {
    border: 1px solid #dee2e6;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    overflow: hidden;
}

.accordion-button {
    background: var(--secondary-color);
    color: var(--text-primary);
    font-weight: 500;
    padding: 1rem 1.25rem;
    border: none;
    text-align: right;
    direction: rtl;
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: var(--white-color);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(44, 90, 160, 0.25);
}

.accordion-button::after {
    margin-right: auto;
    margin-left: 0;
}

.accordion-body {
    background: var(--white-color);
    padding: 1.25rem;
    color: var(--text-secondary);
    line-height: 1.7;
    text-align: right;
    direction: rtl;
}

/* Content Section Components */
.content-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid #dee2e6;
}

.section-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color);
}

.section-title {
    color: var(--primary-color);
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-left: 0.75rem;
    font-size: 1.2em;
}

.section-content {
    line-height: 1.7;
    color: var(--text-secondary);
}

.section-content h1,
.section-content h2,
.section-content h3,
.section-content h4,
.section-content h5,
.section-content h6 {
    color: var(--text-primary);
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.section-content p {
    margin-bottom: 1rem;
}

.section-content ul,
.section-content ol {
    margin-bottom: 1rem;
    padding-right: 1.5rem;
}

.section-content li {
    margin-bottom: 0.5rem;
}

.section-content blockquote {
    background: var(--secondary-color);
    border-right: 4px solid var(--primary-color);
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 6px;
    font-style: italic;
}

.section-content code {
    background: var(--secondary-color);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.9em;
    color: var(--danger-color);
}

.section-content pre {
    background: var(--dark-color);
    color: var(--white-color);
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1rem 0;
}

/* Search Info Components */
.search-info-container {
    margin-top: 2rem;
}

.search-info-container .card {
    border: 1px solid #dee2e6;
    box-shadow: var(--shadow-light);
}

.search-info-container .card-header {
    background: linear-gradient(135deg, var(--info-color) 0%, #0dcaf0 100%);
    color: var(--white-color);
    border-bottom: none;
}

.search-info-container .card-title {
    margin: 0;
    font-weight: 600;
}

.search-info-container .card-title i {
    margin-left: 0.5rem;
}

.search-info-container .card-body {
    background: var(--white-color);
}

.search-info-container .card-body p {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.search-info-container .card-body strong {
    color: var(--text-primary);
}

/* Results Search Components */
.results-search-container {
    background: var(--secondary-color);
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.results-search-container .input-group {
    margin-bottom: 0.5rem;
}

.results-search-container .form-control {
    border-color: #ced4da;
}

.results-search-container .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.results-search-container .btn {
    border-color: #ced4da;
}

.search-stats {
    text-align: center;
}

.search-stats small {
    font-weight: 500;
}

/* Highlight Components */
mark {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: var(--dark-color);
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
    font-weight: 600;
}

/* Alert Components */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d1edff 0%, #a7f3d0 100%);
    color: #065f46;
}

.alert-warning {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
}

.alert-danger {
    background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
    color: #991b1b;
}

.alert-info {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .verse-container,
    .hadith-container,
    .scientific-fact,
    .comparison-container,
    .table-container,
    .faq-container,
    .content-section {
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .comparison-item {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .accordion-button {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .section-title {
        font-size: 1.25rem;
    }
    
    .verse-text,
    .hadith-text {
        font-size: 1rem;
        padding: 0.75rem;
    }
}

@media (max-width: 576px) {
    .verse-header,
    .hadith-header,
    .fact-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .comparison-header h4 {
        font-size: 1.1rem;
    }
    
    .table thead th,
    .table tbody td {
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }
    
    .search-info-container .row {
        flex-direction: column;
    }
    
    .search-info-container .col-md-6 {
        margin-bottom: 1rem;
    }
}
