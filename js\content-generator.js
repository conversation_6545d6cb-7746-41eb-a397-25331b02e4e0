// Content Generator Module - مكتبة الإعجاز العلمي

class ContentGenerator {
    constructor() {
        this.currentResults = null;
        this.searchHistory = [];
        this.templates = this.initializeTemplates();
    }

    // تهيئة القوالب
    initializeTemplates() {
        return {
            verse: (verse, surah, number, interpretation) => `
                <div class="verse-container">
                    <div class="verse-header">
                        <h4 class="verse-title">
                            <i class="fas fa-quran text-primary"></i>
                            سورة ${surah} - الآية ${number}
                        </h4>
                    </div>
                    <div class="verse-content">
                        <blockquote class="verse-text">
                            ${verse}
                        </blockquote>
                        <div class="verse-interpretation">
                            <h5>التفسير العلمي:</h5>
                            <p>${interpretation}</p>
                        </div>
                    </div>
                </div>
            `,

            hadith: (hadith, narrator, grade, interpretation) => `
                <div class="hadith-container">
                    <div class="hadith-header">
                        <h4 class="hadith-title">
                            <i class="fas fa-scroll text-success"></i>
                            حديث شريف
                        </h4>
                        <span class="hadith-grade badge bg-${grade === 'صحيح' ? 'success' : 'warning'}">${grade}</span>
                    </div>
                    <div class="hadith-content">
                        <blockquote class="hadith-text">
                            ${hadith}
                        </blockquote>
                        <p class="hadith-narrator"><strong>الراوي:</strong> ${narrator}</p>
                        <div class="hadith-interpretation">
                            <h5>الشرح العلمي:</h5>
                            <p>${interpretation}</p>
                        </div>
                    </div>
                </div>
            `,

            scientificFact: (title, description, source, year) => `
                <div class="scientific-fact">
                    <div class="fact-header">
                        <h4 class="fact-title">
                            <i class="fas fa-microscope text-info"></i>
                            ${title}
                        </h4>
                        <span class="fact-year badge bg-info">${year}</span>
                    </div>
                    <div class="fact-content">
                        <p>${description}</p>
                        <p class="fact-source"><strong>المصدر:</strong> ${source}</p>
                    </div>
                </div>
            `,

            comparison: (quranicText, scientificFact, analysis) => `
                <div class="comparison-container">
                    <div class="comparison-header">
                        <h4>
                            <i class="fas fa-balance-scale text-warning"></i>
                            مقارنة علمية
                        </h4>
                    </div>
                    <div class="comparison-content">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="comparison-item quran">
                                    <h5><i class="fas fa-book-quran"></i> النص الشرعي</h5>
                                    <p>${quranicText}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="comparison-item science">
                                    <h5><i class="fas fa-flask"></i> الحقيقة العلمية</h5>
                                    <p>${scientificFact}</p>
                                </div>
                            </div>
                        </div>
                        <div class="comparison-analysis">
                            <h5>تحليل التطابق:</h5>
                            <p>${analysis}</p>
                        </div>
                    </div>
                </div>
            `,

            table: (headers, rows, caption) => `
                <div class="table-container">
                    <div class="table-header">
                        <h4>
                            <i class="fas fa-table text-secondary"></i>
                            ${caption}
                        </h4>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-primary">
                                <tr>
                                    ${headers.map(header => `<th>${header}</th>`).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${rows.map(row => `
                                    <tr>
                                        ${row.map(cell => `<td>${cell}</td>`).join('')}
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `,

            faq: (questions) => `
                <div class="faq-container">
                    <div class="faq-header">
                        <h3>
                            <i class="fas fa-question-circle text-info"></i>
                            الأسئلة الشائعة
                        </h3>
                    </div>
                    <div class="accordion" id="faqAccordion">
                        ${questions.map((q, index) => `
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading${index}">
                                    <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" 
                                            type="button" 
                                            data-bs-toggle="collapse" 
                                            data-bs-target="#collapse${index}">
                                        ${q.question}
                                    </button>
                                </h2>
                                <div id="collapse${index}" 
                                     class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" 
                                     data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        ${q.answer}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `,

            section: (title, content, icon = 'fas fa-star') => `
                <section class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="${icon}"></i>
                            ${title}
                        </h2>
                    </div>
                    <div class="section-content">
                        ${content}
                    </div>
                </section>
            `
        };
    }

    // تحويل النص المكتوب بـ Markdown إلى HTML
    parseMarkdownToHTML(markdown) {
        let html = markdown;

        // تحويل العناوين
        html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
        html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
        html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

        // تحويل النص المائل والعريض
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // تحويل القوائم
        html = html.replace(/^\* (.*$)/gim, '<li>$1</li>');
        html = html.replace(/^(\d+)\. (.*$)/gim, '<li>$1. $2</li>');

        // تحويل الاقتباسات
        html = html.replace(/^> (.*$)/gim, '<blockquote class="blockquote">$1</blockquote>');

        // تحويل الروابط
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');

        // تحويل الأسطر الجديدة
        html = html.replace(/\n\n/g, '</p><p>');
        html = html.replace(/\n/g, '<br>');

        // إضافة علامات الفقرة
        if (!html.startsWith('<')) {
            html = '<p>' + html + '</p>';
        }

        return html;
    }

    // استخراج المكونات من النص
    extractComponents(text) {
        const components = {
            verses: [],
            hadiths: [],
            scientificFacts: [],
            comparisons: [],
            tables: [],
            faqs: []
        };

        // استخراج الآيات القرآنية
        const versePattern = /سورة\s+(\w+)\s*-?\s*الآية\s*(\d+)[:\s]*([^]*?)(?=سورة|\n\n|$)/g;
        let verseMatch;
        while ((verseMatch = versePattern.exec(text)) !== null) {
            components.verses.push({
                surah: verseMatch[1],
                number: verseMatch[2],
                text: verseMatch[3].trim()
            });
        }

        // استخراج الأحاديث
        const hadithPattern = /حديث\s*شريف[:\s]*([^]*?)(?=حديث|الراوي|$)/g;
        let hadithMatch;
        while ((hadithMatch = hadithPattern.exec(text)) !== null) {
            components.hadiths.push({
                text: hadithMatch[1].trim()
            });
        }

        // استخراج الحقائق العلمية
        const factPattern = /الحقيقة\s*العلمية[:\s]*([^]*?)(?=الحقيقة|\n\n|$)/g;
        let factMatch;
        while ((factMatch = factPattern.exec(text)) !== null) {
            components.scientificFacts.push({
                text: factMatch[1].trim()
            });
        }

        return components;
    }

    // تنسيق المحتوى الديناميكي
    formatContent(rawContent) {
        if (!rawContent || !rawContent.content) {
            return '<p class="text-muted">لا توجد نتائج للعرض</p>';
        }

        let formattedHTML = '';
        const content = rawContent.content;
        const components = this.extractComponents(content);

        // تقسيم المحتوى إلى أقسام
        const sections = content.split(/^##\s+/gm).filter(section => section.trim());

        sections.forEach((section, index) => {
            if (section.trim()) {
                const lines = section.split('\n');
                const title = lines[0].trim();
                const sectionContent = lines.slice(1).join('\n').trim();

                if (title && sectionContent) {
                    const icon = this.getSectionIcon(title);
                    const processedContent = this.processSection(sectionContent, title);
                    formattedHTML += this.templates.section(title, processedContent, icon);
                }
            }
        });

        // إضافة معلومات البحث
        formattedHTML += this.createSearchInfo(rawContent);

        return formattedHTML;
    }

    // معالجة قسم معين
    processSection(content, title) {
        let processedContent = this.parseMarkdownToHTML(content);

        // معالجة خاصة للجداول
        if (title.includes('جدول') || title.includes('مقارن') || title.includes('إحصائ')) {
            processedContent = this.enhanceTablesInContent(processedContent);
        }

        // معالجة خاصة للأسئلة الشائعة
        if (title.includes('أسئلة') || title.includes('شائعة') || title.includes('FAQ')) {
            processedContent = this.enhanceFAQInContent(processedContent);
        }

        // معالجة خاصة للآيات والأحاديث
        processedContent = this.enhanceReligiousTexts(processedContent);

        return processedContent;
    }

    // تحسين الجداول في المحتوى
    enhanceTablesInContent(content) {
        // البحث عن الجداول البسيطة وتحويلها إلى HTML
        const tablePattern = /\|([^|\n]+\|[^|\n]+)\|/g;
        
        return content.replace(tablePattern, (match) => {
            const rows = match.split('\n').filter(row => row.trim());
            if (rows.length < 2) return match;

            const headers = rows[0].split('|').map(h => h.trim()).filter(h => h);
            const dataRows = rows.slice(2).map(row => 
                row.split('|').map(cell => cell.trim()).filter(cell => cell)
            );

            return this.templates.table(headers, dataRows, 'جدول مقارن');
        });
    }

    // تحسين الأسئلة الشائعة
    enhanceFAQInContent(content) {
        const questions = [];
        const lines = content.split('\n');
        let currentQuestion = null;
        let currentAnswer = '';

        lines.forEach(line => {
            if (line.trim().startsWith('س:') || line.trim().startsWith('السؤال:')) {
                if (currentQuestion) {
                    questions.push({
                        question: currentQuestion,
                        answer: currentAnswer.trim()
                    });
                }
                currentQuestion = line.replace(/^س:\s*|^السؤال:\s*/i, '').trim();
                currentAnswer = '';
            } else if (line.trim().startsWith('ج:') || line.trim().startsWith('الجواب:')) {
                currentAnswer = line.replace(/^ج:\s*|^الجواب:\s*/i, '').trim();
            } else if (currentQuestion && line.trim()) {
                currentAnswer += ' ' + line.trim();
            }
        });

        if (currentQuestion) {
            questions.push({
                question: currentQuestion,
                answer: currentAnswer.trim()
            });
        }

        if (questions.length > 0) {
            return this.templates.faq(questions);
        }

        return content;
    }

    // تحسين النصوص الدينية
    enhanceReligiousTexts(content) {
        // تحسين الآيات القرآنية
        content = content.replace(
            /قال\s+تعالى[:\s]*["""]([^"""]+)["""]/g,
            '<div class="verse-highlight"><i class="fas fa-quran text-primary"></i> قال تعالى: <span class="verse-text">"$1"</span></div>'
        );

        // تحسين الأحاديث
        content = content.replace(
            /قال\s+رسول\s+الله[:\s]*["""]([^"""]+)["""]/g,
            '<div class="hadith-highlight"><i class="fas fa-scroll text-success"></i> قال رسول الله ﷺ: <span class="hadith-text">"$1"</span></div>'
        );

        return content;
    }

    // الحصول على أيقونة القسم
    getSectionIcon(title) {
        const iconMap = {
            'تحليل': 'fas fa-chart-line',
            'آيات': 'fas fa-quran',
            'أحاديث': 'fas fa-scroll',
            'علمي': 'fas fa-microscope',
            'اعتراض': 'fas fa-shield-alt',
            'جدول': 'fas fa-table',
            'أسئلة': 'fas fa-question-circle',
            'مصادر': 'fas fa-book',
            'مقارن': 'fas fa-balance-scale',
            'إحصائ': 'fas fa-chart-bar'
        };

        for (const [keyword, icon] of Object.entries(iconMap)) {
            if (title.includes(keyword)) {
                return icon;
            }
        }

        return 'fas fa-star';
    }

    // إنشاء معلومات البحث
    createSearchInfo(rawContent) {
        return `
            <div class="search-info-container mt-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle text-info"></i>
                            معلومات البحث
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>الموضوع:</strong> ${rawContent.topic}</p>
                                <p><strong>تاريخ البحث:</strong> ${rawContent.timestamp}</p>
                                <p><strong>معرف البحث:</strong> ${rawContent.searchId}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>عدد الكلمات:</strong> ${rawContent.wordCount || 'غير محدد'}</p>
                                <p><strong>عدد الأقسام:</strong> ${rawContent.sections || 'غير محدد'}</p>
                                <p><strong>مستوى التفصيل:</strong> ${this.getDetailLevelText(rawContent.settings?.detailLevel)}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // الحصول على نص مستوى التفصيل
    getDetailLevelText(level) {
        const levelMap = {
            'basic': 'أساسي',
            'intermediate': 'متوسط',
            'advanced': 'متقدم',
            'expert': 'خبير'
        };
        return levelMap[level] || 'غير محدد';
    }

    // إنشاء محرك البحث داخل النتائج
    createSearchEngine(content) {
        return `
            <div class="results-search-container mb-4">
                <div class="input-group">
                    <input type="text" 
                           id="resultsSearchInput" 
                           class="form-control" 
                           placeholder="ابحث في النتائج...">
                    <button class="btn btn-outline-secondary" 
                            type="button" 
                            onclick="searchInResults()">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-outline-secondary" 
                            type="button" 
                            onclick="clearResultsSearch()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="search-stats mt-2">
                    <small class="text-muted" id="searchStats"></small>
                </div>
            </div>
        `;
    }

    // حفظ النتائج الحالية
    saveCurrentResults(results) {
        this.currentResults = results;
        
        // إضافة إلى التاريخ
        this.searchHistory.unshift({
            topic: results.topic,
            timestamp: results.timestamp,
            searchId: results.searchId,
            wordCount: results.wordCount
        });

        // الاحتفاظ بآخر 10 عمليات بحث فقط
        if (this.searchHistory.length > 10) {
            this.searchHistory = this.searchHistory.slice(0, 10);
        }

        // حفظ في التخزين المحلي
        try {
            localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
            localStorage.setItem('currentResults', JSON.stringify(results));
        } catch (error) {
            console.error('خطأ في حفظ النتائج:', error);
        }
    }

    // تحميل النتائج المحفوظة
    loadSavedResults() {
        try {
            const saved = localStorage.getItem('currentResults');
            if (saved) {
                this.currentResults = JSON.parse(saved);
                return this.currentResults;
            }
        } catch (error) {
            console.error('خطأ في تحميل النتائج المحفوظة:', error);
        }
        return null;
    }

    // الحصول على تاريخ البحث
    getSearchHistory() {
        try {
            const saved = localStorage.getItem('searchHistory');
            if (saved) {
                this.searchHistory = JSON.parse(saved);
            }
        } catch (error) {
            console.error('خطأ في تحميل تاريخ البحث:', error);
        }
        return this.searchHistory;
    }

    // مسح تاريخ البحث
    clearSearchHistory() {
        this.searchHistory = [];
        try {
            localStorage.removeItem('searchHistory');
            localStorage.removeItem('currentResults');
        } catch (error) {
            console.error('خطأ في مسح التاريخ:', error);
        }
    }

    // إنشاء ملخص للنتائج
    generateSummary(results) {
        if (!results || !results.content) return '';

        const content = results.content;
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
        
        // اختيار أهم 3-5 جمل
        const importantSentences = sentences
            .slice(0, Math.min(5, sentences.length))
            .map(s => s.trim())
            .filter(s => s.length > 0);

        return importantSentences.join('. ') + '.';
    }
}

// إنشاء مثيل من مولد المحتوى
const contentGenerator = new ContentGenerator();

// تصدير للاستخدام في الملفات الأخرى
window.ContentGenerator = ContentGenerator;
window.contentGenerator = contentGenerator;

console.log('تم تحميل وحدة مولد المحتوى بنجاح');
