// Main JavaScript File - مكتبة الإعجاز العلمي

// متغيرات عامة
let currentSearchTopic = '';
let isSearching = false;
let searchAbortController = null;

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// تهيئة التطبيق
function initializeApp() {
    console.log('بدء تهيئة التطبيق...');
    
    // تحميل الإعدادات المحفوظة
    loadSavedSettings();
    
    // إعداد مستمعي الأحداث
    setupEventListeners();
    
    // تحديث الرابط الكانونيكال
    Utils.updateCanonicalUrl();
    
    // تحميل النتائج المحفوظة إن وجدت
    loadPreviousResults();
    
    // إعداد callback للتقدم
    aiIntegration.setProgressCallback(updateProgress);
    
    console.log('تم تهيئة التطبيق بنجاح');
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // نموذج البحث
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', handleSearch);
    }

    // أزرار التحكم في التقدم
    const pauseBtn = document.getElementById('pauseBtn');
    const stopBtn = document.getElementById('stopBtn');
    
    if (pauseBtn) {
        pauseBtn.addEventListener('click', togglePause);
    }
    
    if (stopBtn) {
        stopBtn.addEventListener('click', stopSearch);
    }

    // أزرار التصدير
    setupExportButtons();
    
    // أزرار الإعدادات
    setupSettingsButtons();
    
    // البحث في النتائج
    const resultsSearchInput = document.getElementById('resultsSearchInput');
    if (resultsSearchInput) {
        resultsSearchInput.addEventListener('input', debounce(searchInResults, 300));
    }

    // تحديث الرابط الكانونيكال عند تغيير URL
    window.addEventListener('popstate', Utils.updateCanonicalUrl);
}

// معالجة البحث
async function handleSearch(event) {
    event.preventDefault();
    
    if (isSearching) {
        showAlert('يتم تنفيذ بحث آخر حالياً. يرجى الانتظار أو إيقافه أولاً.', 'warning');
        return;
    }

    const searchInput = document.getElementById('searchInput');
    const topic = searchInput.value.trim();
    
    if (!topic) {
        showAlert('يرجى إدخال موضوع للبحث', 'warning');
        searchInput.focus();
        return;
    }

    currentSearchTopic = topic;
    
    try {
        // إظهار شاشة التحميل
        showLoadingOverlay();
        
        // الحصول على الإعدادات
        const settings = getCurrentSettings();
        
        // بدء البحث
        isSearching = true;
        updateProgress(0, 'بدء البحث...');

        console.log('بدء معالجة المحتوى للموضوع:', topic);
        console.log('الإعدادات:', settings);

        const results = await aiIntegration.processLargeContent(topic, settings);

        console.log('تم الحصول على النتائج:', results);

        // التحقق من صحة النتائج
        if (!results) {
            throw new Error('لم يتم الحصول على أي نتائج');
        }

        // عرض النتائج
        displayResults(results);

        // حفظ النتائج
        try {
            contentGenerator.saveCurrentResults(results);
            exportManager.setCurrentResults(results);
            console.log('تم حفظ النتائج بنجاح');
        } catch (saveError) {
            console.error('خطأ في حفظ النتائج:', saveError);
            // لا نوقف العملية بسبب خطأ في الحفظ
        }

        // تحديث SEO
        try {
            updateSEOForResults(results);
        } catch (seoError) {
            console.error('خطأ في تحديث SEO:', seoError);
            // لا نوقف العملية بسبب خطأ في SEO
        }

        showAlert('تم إكمال البحث بنجاح!', 'success');
        
    } catch (error) {
        console.error('خطأ في البحث:', error);

        // استخدام معالج الأخطاء المحسن
        if (window.errorHandler) {
            errorHandler.showUserFriendlyError(error, `البحث عن: ${topic}`);
        } else {
            // fallback إذا لم يكن معالج الأخطاء متاحاً
            showAlert(`خطأ في البحث: ${error.message}`, 'danger');
        }

        // في حالة الخطأ، عرض محتوى احتياطي
        if (error.message.includes('API') || error.message.includes('network') || error.message.includes('fetch')) {
            showAlert('سيتم عرض محتوى احتياطي أساسي...', 'info');

            try {
                const fallbackResult = {
                    content: `# الإعجاز العلمي في ${topic}

## مقدمة

نعتذر، حدث خطأ في الاتصال بخدمة الذكاء الاصطناعي. يرجى المحاولة مرة أخرى لاحقاً.

## نصائح للحصول على أفضل النتائج

- **تأكد من اتصالك بالإنترنت**: تحقق من أن الاتصال مستقر وسريع
- **جرب موضوع آخر**: قد يكون الموضوع الحالي معقداً جداً
- **أعد تحميل الصفحة**: أحياناً يساعد تحديث الصفحة في حل المشاكل
- **استخدم صفحة الاختبار**: افتح \`test.html\` لتشخيص المشكلة

## مواضيع مقترحة للبحث

- الفضاء والكون
- الطب والصحة
- علوم البحار
- الجيولوgia وعلوم الأرض
- الفيزياء والكيمياء

## الحصول على المساعدة

إذا استمرت المشكلة، يرجى:
1. فتح صفحة الاختبار للتشخيص
2. التحقق من وحدة تحكم المطور (F12)
3. التأكد من صحة مفاتيح API`,
                    topic: topic,
                    timestamp: Utils.formatDate(),
                    searchId: Utils.generateId(),
                    settings: settings,
                    wordCount: 200,
                    sections: 4
                };

                displayResults(fallbackResult);
            } catch (fallbackError) {
                console.error('خطأ في عرض المحتوى الاحتياطي:', fallbackError);
                if (window.errorHandler) {
                    errorHandler.logError({
                        type: 'Fallback Error',
                        message: fallbackError.message,
                        context: 'عرض المحتوى الاحتياطي'
                    });
                }
            }
        }

    } finally {
        isSearching = false;
        hideLoadingOverlay();
    }
}

// البحث السريع
function quickSearch(topic) {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = topic;
        scrollToSearch();
        
        // تأخير قصير ثم بدء البحث
        setTimeout(() => {
            const searchForm = document.getElementById('searchForm');
            if (searchForm) {
                searchForm.dispatchEvent(new Event('submit'));
            }
        }, 500);
    }
}

// التمرير إلى قسم البحث
function scrollToSearch() {
    const searchSection = document.getElementById('search');
    if (searchSection) {
        searchSection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// الحصول على الإعدادات الحالية
function getCurrentSettings() {
    const settings = { ...DEFAULT_SETTINGS };
    
    // قراءة الإعدادات من النموذج
    const detailLevel = document.getElementById('detailLevel');
    if (detailLevel) settings.detailLevel = detailLevel.value;
    
    const sourceType = document.getElementById('sourceType');
    if (sourceType) settings.sourceType = sourceType.value;
    
    const includeTables = document.getElementById('includeTables');
    if (includeTables) settings.includeTables = includeTables.checked;
    
    const includeFAQ = document.getElementById('includeFAQ');
    if (includeFAQ) settings.includeFAQ = includeFAQ.checked;
    
    return settings;
}

// تحميل الإعدادات المحفوظة
function loadSavedSettings() {
    const settings = Utils.loadSettings();
    
    // تطبيق الإعدادات على النموذج
    const detailLevel = document.getElementById('detailLevel');
    if (detailLevel && settings.detailLevel) {
        detailLevel.value = settings.detailLevel;
    }
    
    const sourceType = document.getElementById('sourceType');
    if (sourceType && settings.sourceType) {
        sourceType.value = settings.sourceType;
    }
    
    const includeTables = document.getElementById('includeTables');
    if (includeTables && typeof settings.includeTables === 'boolean') {
        includeTables.checked = settings.includeTables;
    }
    
    const includeFAQ = document.getElementById('includeFAQ');
    if (includeFAQ && typeof settings.includeFAQ === 'boolean') {
        includeFAQ.checked = settings.includeFAQ;
    }
}

// تحديث التقدم
function updateProgress(percentage, message) {
    const overlay = document.getElementById('loadingOverlay');
    if (!overlay || overlay.classList.contains('d-none')) return;
    
    const progressBar = overlay.querySelector('.progress-bar');
    const progressText = overlay.querySelector('.progress-text');
    const loadingText = overlay.querySelector('.loading-text');
    
    if (percentage !== null && progressBar && progressText) {
        progressBar.style.width = percentage + '%';
        progressText.textContent = Math.round(percentage) + '%';
    }
    
    if (message && loadingText) {
        loadingText.textContent = message;
    }
}

// إظهار شاشة التحميل
function showLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.remove('d-none');
        updateProgress(0, 'جاري التحضير...');
    }
}

// إخفاء شاشة التحميل
function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.add('d-none');
    }
}

// تبديل الإيقاف المؤقت
function togglePause() {
    const pauseBtn = document.getElementById('pauseBtn');
    if (!pauseBtn) return;
    
    if (aiIntegration.isPaused) {
        aiIntegration.resume();
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i> إيقاف مؤقت';
        pauseBtn.classList.remove('btn-success');
        pauseBtn.classList.add('btn-warning');
    } else {
        aiIntegration.pause();
        pauseBtn.innerHTML = '<i class="fas fa-play"></i> استئناف';
        pauseBtn.classList.remove('btn-warning');
        pauseBtn.classList.add('btn-success');
    }
}

// إيقاف البحث
function stopSearch() {
    if (isSearching) {
        aiIntegration.stop();
        isSearching = false;
        hideLoadingOverlay();
        showAlert('تم إيقاف البحث', 'info');
    }
}

// عرض النتائج
function displayResults(results) {
    console.log('🔍 بدء عرض النتائج:', results);
    console.log('🔍 نوع النتائج:', typeof results);
    console.log('🔍 هل النتائج موجودة؟', !!results);
    console.log('🔍 هل المحتوى موجود؟', !!(results && results.content));

    const resultsSection = document.getElementById('results');
    const resultsContent = document.getElementById('resultsContent');

    console.log('🔍 عنصر النتائج:', resultsSection);
    console.log('🔍 عنصر المحتوى:', resultsContent);

    if (!resultsSection || !resultsContent) {
        console.error('❌ عناصر عرض النتائج غير موجودة');
        console.error('❌ resultsSection:', resultsSection);
        console.error('❌ resultsContent:', resultsContent);
        showAlert('خطأ في عرض النتائج: عناصر العرض غير موجودة', 'danger');
        return;
    }

    if (!results) {
        console.error('❌ النتائج غير موجودة تماماً');
        showAlert('لا توجد نتائج للعرض - النتائج غير موجودة', 'warning');
        return;
    }

    if (!results.content) {
        console.error('❌ محتوى النتائج غير موجود');
        console.error('❌ خصائص النتائج:', Object.keys(results));
        showAlert('لا توجد نتائج للعرض - المحتوى غير موجود', 'warning');
        return;
    }

    console.log('✅ جميع الفحوصات نجحت، بدء التنسيق...');

    try {
        // تنسيق المحتوى
        console.log('🎨 بدء تنسيق المحتوى...');
        const formattedContent = contentGenerator.formatContent(results);
        console.log('🎨 تم تنسيق المحتوى بنجاح، طول:', formattedContent.length);
        console.log('🎨 أول 300 حرف من المحتوى المنسق:', formattedContent.substring(0, 300));

        // إضافة محرك البحث
        console.log('🔍 إضافة محرك البحث...');
        const searchEngine = contentGenerator.createSearchEngine();
        console.log('🔍 تم إنشاء محرك البحث، طول:', searchEngine.length);

        // عرض المحتوى
        const finalContent = searchEngine + formattedContent;
        console.log('📄 المحتوى النهائي، طول:', finalContent.length);

        resultsContent.innerHTML = finalContent;
        console.log('✅ تم إدراج المحتوى في الصفحة');
        console.log('✅ محتوى العنصر الآن:', resultsContent.innerHTML.length, 'حرف');

        // إظهار قسم النتائج
        resultsSection.classList.remove('d-none');
        console.log('✅ تم إظهار قسم النتائج');

        // التمرير إلى النتائج
        setTimeout(() => {
            resultsSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }, 100);

        // تحديث عنوان الصفحة
        document.title = `الإعجاز العلمي في ${results.topic} - مكتبة الإعجاز العلمي`;

        console.log('تم عرض النتائج بنجاح');

    } catch (error) {
        console.error('خطأ في عرض النتائج:', error);
        showAlert(`خطأ في عرض النتائج: ${error.message}`, 'danger');

        // عرض محتوى احتياطي
        resultsContent.innerHTML = `
            <div class="alert alert-warning">
                <h4>تم إنتاج النتائج ولكن حدث خطأ في العرض</h4>
                <p>الموضوع: ${results.topic}</p>
                <p>تاريخ البحث: ${results.timestamp}</p>
                <p>معرف البحث: ${results.searchId}</p>
                <hr>
                <h5>المحتوى الخام:</h5>
                <pre style="white-space: pre-wrap; direction: rtl; text-align: right;">${results.content}</pre>
            </div>
        `;
        resultsSection.classList.remove('d-none');
    }
}

// البحث في النتائج
function searchInResults() {
    const searchInput = document.getElementById('resultsSearchInput');
    const searchStats = document.getElementById('searchStats');
    
    if (!searchInput) return;
    
    const query = searchInput.value.toLowerCase().trim();
    const resultsContent = document.getElementById('resultsContent');
    
    if (!resultsContent) return;
    
    // إزالة التمييز السابق
    const highlighted = resultsContent.querySelectorAll('mark');
    highlighted.forEach(mark => {
        const parent = mark.parentNode;
        parent.replaceChild(document.createTextNode(mark.textContent), mark);
        parent.normalize();
    });
    
    if (!query) {
        if (searchStats) searchStats.textContent = '';
        return;
    }
    
    // البحث والتمييز
    let matchCount = 0;
    const walker = document.createTreeWalker(
        resultsContent,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );
    
    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
        textNodes.push(node);
    }
    
    textNodes.forEach(textNode => {
        const text = textNode.textContent;
        const regex = new RegExp(query, 'gi');
        const matches = text.match(regex);
        
        if (matches) {
            matchCount += matches.length;
            const highlightedText = text.replace(regex, '<mark>$&</mark>');
            const wrapper = document.createElement('span');
            wrapper.innerHTML = highlightedText;
            textNode.parentNode.replaceChild(wrapper, textNode);
        }
    });
    
    // تحديث الإحصائيات
    if (searchStats) {
        if (matchCount > 0) {
            searchStats.textContent = `تم العثور على ${matchCount} نتيجة`;
            searchStats.className = 'text-success';
        } else {
            searchStats.textContent = 'لم يتم العثور على نتائج';
            searchStats.className = 'text-muted';
        }
    }
}

// مسح البحث في النتائج
function clearResultsSearch() {
    const searchInput = document.getElementById('resultsSearchInput');
    if (searchInput) {
        searchInput.value = '';
        searchInResults();
    }
}

// إعداد أزرار التصدير
function setupExportButtons() {
    // تصدير HTML
    const htmlBtn = document.querySelector('[onclick="exportResults(\'html\')"]');
    if (htmlBtn) {
        htmlBtn.onclick = () => exportResults('html');
    }
    
    // تصدير PDF
    const pdfBtn = document.querySelector('[onclick="exportResults(\'pdf\')"]');
    if (pdfBtn) {
        pdfBtn.onclick = () => exportResults('pdf');
    }
    
    // تصدير Excel
    const excelBtn = document.querySelector('[onclick="exportResults(\'excel\')"]');
    if (excelBtn) {
        excelBtn.onclick = () => exportResults('excel');
    }
    
    // تصدير نص
    const txtBtn = document.querySelector('[onclick="exportResults(\'txt\')"]');
    if (txtBtn) {
        txtBtn.onclick = () => exportResults('txt');
    }
    
    // تصدير مضغوط
    const zipBtn = document.querySelector('[onclick="exportResults(\'zip\')"]');
    if (zipBtn) {
        zipBtn.onclick = () => exportResults('zip');
    }
}

// تصدير النتائج
async function exportResults(format) {
    const results = exportManager.getCurrentResults();
    
    if (!results) {
        showAlert('لا توجد نتائج للتصدير. يرجى إجراء بحث أولاً.', 'warning');
        return;
    }
    
    try {
        showAlert('جاري تحضير الملف للتصدير...', 'info');
        
        let exportResult;
        
        switch (format) {
            case 'html':
                exportResult = await exportManager.exportToHTML(results);
                exportManager.downloadFile(
                    exportResult.content,
                    exportResult.filename,
                    exportResult.mimeType
                );
                break;
                
            case 'pdf':
                exportResult = await exportManager.exportToPDF(results);
                showAlert(exportResult.message, 'info');
                return;
                
            case 'excel':
                exportResult = await exportManager.exportToExcel(results);
                exportManager.downloadFile(
                    exportResult.content,
                    exportResult.filename,
                    exportResult.mimeType
                );
                break;
                
            case 'txt':
                exportResult = await exportManager.exportToText(results);
                exportManager.downloadFile(
                    exportResult.content,
                    exportResult.filename,
                    exportResult.mimeType
                );
                break;
                
            case 'zip':
                exportResult = await exportManager.exportToZip(results);
                exportResult.files.forEach(file => {
                    exportManager.downloadFile(
                        file.content,
                        file.filename,
                        file.mimeType
                    );
                });
                showAlert(exportResult.message, 'info');
                return;
                
            default:
                throw new Error('صيغة تصدير غير مدعومة');
        }
        
        showAlert('تم تصدير الملف بنجاح!', 'success');
        
    } catch (error) {
        console.error('خطأ في التصدير:', error);
        showAlert(`خطأ في التصدير: ${error.message}`, 'danger');
    }
}

// إعداد أزرار الإعدادات
function setupSettingsButtons() {
    // تصدير الإعدادات
    const exportSettingsBtn = document.querySelector('[onclick="exportSettings()"]');
    if (exportSettingsBtn) {
        exportSettingsBtn.onclick = exportSettings;
    }
    
    // استيراد الإعدادات
    const importSettingsBtn = document.querySelector('[onclick="importSettings()"]');
    if (importSettingsBtn) {
        importSettingsBtn.onclick = importSettings;
    }
    
    // ملف الاستيراد المخفي
    const fileInput = document.getElementById('settingsFileInput');
    if (fileInput) {
        fileInput.addEventListener('change', handleSettingsFileImport);
    }
}

// تصدير الإعدادات
function exportSettings() {
    try {
        exportManager.exportSettings();
        showAlert('تم تصدير الإعدادات بنجاح!', 'success');
    } catch (error) {
        console.error('خطأ في تصدير الإعدادات:', error);
        showAlert(`خطأ في تصدير الإعدادات: ${error.message}`, 'danger');
    }
}

// استيراد الإعدادات
function importSettings() {
    const fileInput = document.getElementById('settingsFileInput');
    if (fileInput) {
        fileInput.click();
    }
}

// معالجة استيراد ملف الإعدادات
async function handleSettingsFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
        const result = await exportManager.importSettings(file);
        showAlert(result.message, 'success');
        
        // إعادة تعيين قيمة الملف
        event.target.value = '';
        
    } catch (error) {
        console.error('خطأ في استيراد الإعدادات:', error);
        showAlert(`خطأ في استيراد الإعدادات: ${error.message}`, 'danger');
        
        // إعادة تعيين قيمة الملف
        event.target.value = '';
    }
}

// تحميل النتائج السابقة
function loadPreviousResults() {
    try {
        const savedResults = contentGenerator.loadSavedResults();
        if (savedResults) {
            console.log('🔄 تم العثور على نتائج محفوظة:', savedResults.topic);
            console.log('🔄 تفاصيل النتائج المحفوظة:', {
                topic: savedResults.topic,
                hasContent: !!savedResults.content,
                contentLength: savedResults.content ? savedResults.content.length : 0,
                timestamp: savedResults.timestamp,
                searchId: savedResults.searchId
            });

            exportManager.setCurrentResults(savedResults);

            // عرض النتائج المحفوظة تلقائياً
            console.log('🔄 محاولة عرض النتائج المحفوظة...');
            displayResults(savedResults);
        } else {
            console.log('🔄 لا توجد نتائج محفوظة');
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل النتائج المحفوظة:', error);
        if (window.errorHandler) {
            errorHandler.logError({
                type: 'Load Previous Results Error',
                message: error.message,
                context: 'تحميل النتائج السابقة'
            });
        }
    }
}

// تحديث SEO للنتائج
function updateSEOForResults(results) {
    // تحديث العنوان
    const title = `الإعجاز العلمي في ${results.topic} - مكتبة الإعجاز العلمي`;
    document.title = title;
    
    // تحديث الوصف
    const description = `اكتشف المعجزات العلمية في القرآن والسنة حول ${results.topic}. تحليل شامل ومفصل بالذكاء الاصطناعي.`;
    
    let metaDesc = document.querySelector('meta[name="description"]');
    if (metaDesc) {
        metaDesc.content = description;
    }
    
    // تحديث Open Graph
    let ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle) {
        ogTitle.content = title;
    }
    
    let ogDesc = document.querySelector('meta[property="og:description"]');
    if (ogDesc) {
        ogDesc.content = description;
    }
    
    // تحديث البيانات المنظمة
    Utils.updateStructuredData({
        name: title,
        description: description,
        dateModified: new Date().toISOString()
    });
    
    // تحديث الرابط الكانونيكال
    Utils.updateCanonicalUrl();
}

// عرض رسالة تنبيه
function showAlert(message, type = 'info') {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 10000; max-width: 400px;';
    
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // إضافة إلى الصفحة
    document.body.appendChild(alertDiv);
    
    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// عرض العرض التوضيحي
function showDemo() {
    // إنشاء نافذة منبثقة للعرض التوضيحي
    const demoModal = `
        <div class="modal fade" id="demoModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-play-circle"></i>
                            العرض التوضيحي - مكتبة الإعجاز العلمي
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-search text-primary"></i> كيفية البحث:</h6>
                                <ol>
                                    <li>أدخل موضوع علمي (مثل: الفضاء)</li>
                                    <li>اختر الإعدادات المناسبة</li>
                                    <li>اضغط "ابحث"</li>
                                    <li>انتظر النتائج</li>
                                </ol>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-lightbulb text-warning"></i> نصائح مهمة:</h6>
                                <ul>
                                    <li>ابدأ بمواضيع بسيطة</li>
                                    <li>تحقق من الاتصال بالإنترنت</li>
                                    <li>استخدم صفحة الاختبار عند المشاكل</li>
                                    <li>احفظ النتائج المهمة</li>
                                </ul>
                            </div>
                        </div>

                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-rocket"></i> جرب الآن:</h6>
                            <p>اضغط على أحد المواضيع التالية لبدء بحث تجريبي:</p>
                            <div class="d-flex flex-wrap gap-2">
                                <button class="btn btn-sm btn-outline-primary" onclick="demoSearch('الفضاء')">الفضاء</button>
                                <button class="btn btn-sm btn-outline-primary" onclick="demoSearch('الطب')">الطب</button>
                                <button class="btn btn-sm btn-outline-primary" onclick="demoSearch('البحار')">البحار</button>
                                <button class="btn btn-sm btn-outline-primary" onclick="demoSearch('الجيولوجيا')">الجيولوجيا</button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="quick-start.html" class="btn btn-success">
                            <i class="fas fa-book"></i>
                            دليل البدء السريع
                        </a>
                        <a href="test.html" class="btn btn-warning">
                            <i class="fas fa-vial"></i>
                            صفحة الاختبار
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة النافذة المنبثقة إلى الصفحة
    const existingModal = document.getElementById('demoModal');
    if (existingModal) {
        existingModal.remove();
    }

    document.body.insertAdjacentHTML('beforeend', demoModal);

    // عرض النافذة المنبثقة
    const modal = new bootstrap.Modal(document.getElementById('demoModal'));
    modal.show();
}

// بحث تجريبي من العرض التوضيحي
function demoSearch(topic) {
    // إغلاق النافذة المنبثقة
    const modal = bootstrap.Modal.getInstance(document.getElementById('demoModal'));
    if (modal) {
        modal.hide();
    }

    // تعبئة مربع البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = topic;
    }

    // التمرير إلى قسم البحث
    scrollToSearch();

    // تأخير قصير ثم بدء البحث
    setTimeout(() => {
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.dispatchEvent(new Event('submit'));
        }
    }, 1000);
}

// دالة debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// حفظ الإعدادات عند تغييرها
function saveCurrentSettings() {
    const settings = getCurrentSettings();
    Utils.saveSettings(settings);
}

// إضافة مستمعي الأحداث لحفظ الإعدادات
document.addEventListener('DOMContentLoaded', function() {
    const settingsInputs = [
        'detailLevel',
        'sourceType',
        'includeTables',
        'includeFAQ'
    ];
    
    settingsInputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', saveCurrentSettings);
        }
    });
});

// مسح جميع البيانات المحفوظة
function clearAllData() {
    console.log('🧹 مسح جميع البيانات المحفوظة...');

    const keys = [
        'currentResults',
        'scientificMiraclesResults',
        'searchHistory',
        'scientificMiraclesSettings',
        'scientificMiraclesErrorLog'
    ];

    keys.forEach(key => {
        localStorage.removeItem(key);
        console.log(`🧹 تم مسح ${key}`);
    });

    showAlert('تم مسح جميع البيانات المحفوظة', 'success');

    // إعادة تحميل الصفحة
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// دالة اختبار سريعة لعرض النتائج
function testDisplayResults() {
    console.log('🧪 بدء اختبار عرض النتائج...');

    // مسح المحتوى السابق أولاً
    const resultsContent = document.getElementById('resultsContent');
    if (resultsContent) {
        resultsContent.innerHTML = '';
        console.log('🧪 تم مسح المحتوى السابق');
    }

    const testResults = {
        content: `# الإعجاز العلمي في الطب

## مقدمة
الطب في القرآن الكريم والسنة النبوية يحتوي على إعجاز علمي مذهل يؤكد صدق الرسالة المحمدية.

## الآيات القرآنية
قال تعالى: "وَنُنَزِّلُ مِنَ الْقُرْآنِ مَا هُوَ شِفَاءٌ وَرَحْمَةٌ لِّلْمُؤْمِنِينَ" (الإسراء: 82)

وقال سبحانه: "وَفِي أَنفُسِكُمْ أَفَلَا تُبْصِرُونَ" (الذاريات: 21)

## الأحاديث النبوية
قال رسول الله صلى الله عليه وسلم: "ما أنزل الله داء إلا أنزل له شفاء"

وقال عليه الصلاة والسلام: "في الحبة السوداء شفاء من كل داء إلا السام"

## الحقائق العلمية الحديثة

### العسل كمضاد حيوي طبيعي
أثبتت الدراسات الحديثة أن العسل يحتوي على:
- مضادات حيوية طبيعية قوية
- إنزيمات تساعد في الشفاء
- مركبات مضادة للالتهابات

### الحجامة وفوائدها الطبية
تساعد الحجامة في:
- تحسين الدورة الدموية
- تخفيف الألم المزمن
- تنشيط جهاز المناعة

### الصوم وتأثيره على الصحة
يساعد الصوم في:
- تجديد الخلايا (Autophagy)
- تحسين المناعة
- تنظيم مستوى السكر في الدم

## الخلاصة والتأملات
هذا مثال واضح على الإعجاز العلمي في الطب الإسلامي، حيث سبق القرآن والسنة العلم الحديث بقرون عديدة.

إن هذا التطابق بين النصوص الشرعية والاكتشافات العلمية الحديثة يؤكد صدق الرسالة المحمدية وأن القرآن الكريم كتاب هداية وعلم.`,
        topic: 'الطب',
        timestamp: new Date().toLocaleString('ar-SA'),
        searchId: 'test-display-' + Date.now(),
        wordCount: 350,
        sections: 6,
        settings: {
            detailLevel: 'متوسط',
            includeQuran: true,
            includeSunnah: true,
            includeTables: false,
            includeFAQ: false
        }
    };

    console.log('🧪 النتائج التجريبية:', testResults);

    try {
        displayResults(testResults);
        console.log('🧪 تم استدعاء displayResults بنجاح');
    } catch (error) {
        console.error('🧪 خطأ في اختبار العرض:', error);
    }
}

// تصدير الدوال للاستخدام العام
window.quickSearch = quickSearch;
window.scrollToSearch = scrollToSearch;
window.showDemo = showDemo;
window.demoSearch = demoSearch;
window.testDisplayResults = testDisplayResults;
window.clearAllData = clearAllData;
window.exportResults = exportResults;
window.exportSettings = exportSettings;
window.importSettings = importSettings;
window.searchInResults = searchInResults;
window.clearResultsSearch = clearResultsSearch;

console.log('تم تحميل الملف الرئيسي بنجاح');
