# مكتبة الإعجاز العلمي - Scientific Miracles Library

## نظرة عامة

مكتبة الإعجاز العلمي هي أداة ذكية ومتطورة تستخدم أحدث تقنيات الذكاء الاصطناعي لاستكشاف وتحليل المعجزات العلمية في القرآن الكريم والسنة النبوية الشريفة. تهدف الأداة إلى تقديم تحليل شامل ومفصل لأي مجال علمي يختاره المستخدم، مع عرض الآيات والأحاديث ذات الصلة والحقائق العلمية المؤكدة.

## المميزات الرئيسية

### 🤖 ذكاء اصطناعي متقدم
- استخدام Gemini API مع تدوير ذكي للمفاتيح
- معالجة المحتوى الكبير بتقسيمه إلى أجزاء قابلة للإدارة
- تحليل متعمق ودقيق للمعلومات العلمية والشرعية

### 📚 قاعدة بيانات شاملة
- مصادر موثقة من القرآن الكريم والسنة النبوية
- ربط مع أحدث الاكتشافات العلمية
- تغطية أكثر من 50 مجال علمي

### 🔍 بحث متقدم
- خيارات بحث متعددة المستويات (أساسي، متوسط، متقدم، خبير)
- فلترة حسب نوع المصادر (القرآن، السنة، أو كليهما)
- محرك بحث داخلي في النتائج

### 📊 عرض احترافي
- تنسيق ديناميكي للمحتوى
- جداول مقارنة تفاعلية
- أسئلة شائعة منظمة
- تصميم متجاوب لجميع الأجهزة

### 📤 تصدير متعدد الصيغ
- HTML تفاعلي مع محرك بحث
- PDF للطباعة والأرشفة
- Excel/CSV للبيانات المنظمة
- ملفات نصية بسيطة
- ملفات مضغوطة شاملة

### ⚙️ إدارة الإعدادات
- حفظ واستيراد الإعدادات
- تخصيص مستوى التفصيل
- اختيار نوع المصادر المطلوبة

## التقنيات المستخدمة

### Frontend
- **HTML5**: هيكل الصفحة مع دعم RTL
- **CSS3**: تصميم حديث مع Bootstrap RTL
- **JavaScript ES6+**: وظائف تفاعلية متقدمة
- **Bootstrap 5.3**: إطار عمل UI متجاوب
- **Font Awesome**: أيقونات احترافية
- **Google Fonts**: خطوط Cairo و Tajawal

### Backend Integration
- **Gemini API**: ذكاء اصطناعي من Google
- **Multiple API Keys**: تدوير ذكي لتجنب حدود الاستخدام
- **Rate Limiting**: إدارة معدل الطلبات
- **Error Handling**: معالجة شاملة للأخطاء

### Features
- **Progressive Web App**: تجربة تطبيق أصلي
- **Local Storage**: حفظ الإعدادات والنتائج
- **Export System**: تصدير متعدد الصيغ
- **Search Engine**: بحث متقدم في النتائج
- **Responsive Design**: تصميم متجاوب

## هيكل المشروع

```
scientific-miracles-library/
├── index.html              # الصفحة الرئيسية
├── css/
│   ├── main.css           # الأنماط الرئيسية
│   ├── components.css     # أنماط المكونات
│   └── animations.css     # الحركات والتأثيرات
├── js/
│   ├── config.js          # إعدادات التطبيق
│   ├── ai-integration.js  # تكامل الذكاء الاصطناعي
│   ├── content-generator.js # مولد المحتوى
│   ├── export-manager.js  # مدير التصدير
│   └── main.js           # الوظائف الرئيسية
├── assets/               # الملفات المساعدة
├── exports/             # ملفات التصدير
└── README.md           # هذا الملف
```

## طريقة الاستخدام

### 1. البحث الأساسي
1. افتح الصفحة الرئيسية
2. أدخل المجال العلمي في مربع البحث (مثل: الفضاء، الطب، الجيولوجيا)
3. اضغط على زر "ابحث"
4. انتظر حتى اكتمال التحليل

### 2. الخيارات المتقدمة
- **مستوى التفصيل**: اختر من أساسي إلى خبير
- **نوع المصادر**: حدد القرآن فقط، السنة فقط، أو كليهما
- **تضمين الجداول**: لإضافة جداول مقارنة
- **الأسئلة الشائعة**: لإضافة قسم الأسئلة والأجوبة

### 3. عرض النتائج
- النتائج تظهر في أقسام منظمة
- يمكن البحث داخل النتائج
- إمكانية التمرير السلس بين الأقسام

### 4. التصدير
- اختر صيغة التصدير المطلوبة
- HTML: للعرض التفاعلي
- PDF: للطباعة والأرشفة
- Excel: للبيانات المنظمة
- نص: للاستخدام البسيط

## إعدادات API

### Gemini API Keys
يستخدم التطبيق 15 مفتاح API مختلف مع تدوير ذكي:
- تبديل تلقائي عند الوصول لحد الاستخدام
- إعادة تعيين العدادات عند الحاجة
- تأخير بين الطلبات لتجنب Rate Limiting

### إدارة الأخطاء
- إعادة المحاولة التلقائية
- رسائل خطأ واضحة
- تسجيل مفصل للأخطاء

## الأمان والخصوصية

### حماية البيانات
- لا يتم حفظ البيانات على خوادم خارجية
- التخزين المحلي فقط في المتصفح
- عدم مشاركة المعلومات الشخصية

### أمان API
- مفاتيح API محمية
- طلبات مشفرة عبر HTTPS
- معالجة آمنة للاستجابات

## متطلبات النظام

### متصفح الويب
- Chrome 80+ (مُوصى به)
- Firefox 75+
- Safari 13+
- Edge 80+

### اتصال الإنترنت
- مطلوب للوصول إلى Gemini API
- سرعة مُوصى بها: 1 Mbps أو أكثر

### الأجهزة المدعومة
- أجهزة الكمبيوتر المكتبية
- الأجهزة اللوحية
- الهواتف الذكية

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. لا يعمل البحث
- تأكد من اتصال الإنترنت
- تحقق من وجود JavaScript مُفعل
- امسح ذاكرة التخزين المؤقت

#### 2. بطء في النتائج
- قد يكون بسبب حجم المحتوى الكبير
- انتظر حتى اكتمال العملية
- تحقق من سرعة الإنترنت

#### 3. أخطاء API
- قد تكون المفاتيح مستنفدة مؤقتاً
- انتظر قليلاً وأعد المحاولة
- تحقق من رسالة الخطأ

#### 4. مشاكل التصدير
- تأكد من السماح بتحميل الملفات
- تحقق من مساحة التخزين المتاحة
- جرب صيغة تصدير أخرى

## التطوير والمساهمة

### إعداد بيئة التطوير
1. استنسخ المشروع
2. افتح `index.html` في متصفح حديث
3. استخدم أدوات المطور للاختبار

### هيكل الكود
- **Modular Design**: كل وحدة في ملف منفصل
- **ES6 Classes**: استخدام الفئات الحديثة
- **Async/Await**: معالجة غير متزامنة
- **Error Handling**: معالجة شاملة للأخطاء

### إرشادات المساهمة
1. اتبع نمط الكود الموجود
2. أضف تعليقات باللغة العربية
3. اختبر التغييرات قبل الإرسال
4. حدث الوثائق عند الحاجة

## الترخيص والحقوق

### حقوق الطبع والنشر
© 2024 مكتبة الإعجاز العلمي. جميع الحقوق محفوظة.

### الاستخدام المسموح
- الاستخدام الشخصي والتعليمي
- البحث الأكاديمي
- الأغراض الدعوية غير التجارية

### القيود
- عدم الاستخدام التجاري بدون إذن
- عدم إعادة توزيع الكود بدون إذن
- احترام حقوق المصادر المرجعية

## الدعم والتواصل

### الحصول على المساعدة
- راجع قسم استكشاف الأخطاء أولاً
- تحقق من وجود تحديثات
- تواصل مع فريق الدعم

### التحديثات المستقبلية
- إضافة مجالات علمية جديدة
- تحسين خوارزميات الذكاء الاصطناعي
- دعم لغات إضافية
- تطبيق جوال مخصص

## الشكر والتقدير

### المصادر المستخدمة
- القرآن الكريم وعلومه
- كتب السنة النبوية الصحيحة
- المراجع العلمية المعتمدة
- مجتمع المطورين مفتوح المصدر

### التقنيات المساعدة
- Google Gemini AI
- Bootstrap Framework
- Font Awesome Icons
- Google Fonts

---

**ملاحظة**: هذا المشروع تعليمي وبحثي، ويهدف إلى إبراز الإعجاز العلمي في القرآن والسنة باستخدام التقنيات الحديثة. جميع المعلومات المعروضة تحتاج للمراجعة والتحقق من المصادر الأصلية.

**تاريخ آخر تحديث**: سبتمبر 2024  
**الإصدار**: 1.0.0
