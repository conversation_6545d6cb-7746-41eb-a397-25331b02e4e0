قم ببرمجة صفحة HTML احترافية جدا 
عبارة عن أداة تستخدم الذكاء الصناعي لتوليد بيانات تفصيلية حول الاعجاز العلمي لأي مجال يتم ادخاله

اسم المشروع أو الأداة: مكتبة الإعجاز العلمي 

الوظيفة: 
- القدرة على التعامل مع احجام بيانات كبيرة أو الملفات بطريقة احترافية متقدمة مع تقسيم العمل بين ال APIs بحيث لا يؤثر حجم التوكنز وعدد العمليات المسموح به على العملية
- الوظيفة الاساسية للأداة ان الشخص يضع في مربع البحث مجال معين او تخصص معين، ثم تقوم الاداة بالبحث المعمق بالذكاء الصناعي ثم اخراج رد متكامل جدا ودقيق جدا يوضح الاعجاز الذي يقدمه القران الكريم والسنة في هذا التخصص او المجال 

المدخلات: 
- مجال مثل العلم، الجيولوجيا، الفضاء ..الخ او كلمة معينة او نص 

المخرجات: 
- رد منسق متكامل يذكر الايات والاحاديث والمواقف الحياتية للرسول والسنة التي فيها اعجاز في المجال المدخل 
- جداول منسقة باحترافية واتقان ودقة تبسط الموضوع 
- عرض الآيات الكريمة والأحاديث والسنن ..الخ > ثم شرحها واسباب الاعجاز فيها > وعرض الاساس العلمي > عرض دقة التوافق وقوة الحجة > لماذا ليس ذلك مجرد تأويل للاية > وهل المعلومة العلمية حقيقة مؤكدة ام نظرية ام فرضية ام قابلة للتغيير والتبدل ..الخ 
- عرض التفسيرات القديمة ولماذا هي اضعف من التفسير الحالي المطابق للعلم؟ .. الخ 
- اوجه الاعتراض على الاعجاز وتفنيده بدقة 
- الاسئلة الشائعة حول الموضوع 
- المصادر العلمية، والمصادر الشرعية 
- يجب ان تكون جميع المعلومات والمناقشات دقيقة 100% 
كل ذلك في تمبلت احترافي جدا وطريقة عرض مبتكرة ومميزة إن شاء الله تعالى 
مع قابلية تصدير المعلومات الناتجة او الاحتفاظ بها او مشاركتها .. الخ 


ملاحظة مهمة: 
يجب ان تكون الأداة قادرة على التعامل مع البيانات الكبيرة 
والمحتوى الكبير فيتم تقسيم الاوامر والعمل عليه ووقت الانتظار بطريقة احترافية حتى لا يتم استنفاذ التوكن المخصصة لل api وعدد التعديلات المسموح والمطالبات في المرة الواحدة


-------
اللغة: 
- العربية 

لغة البرمجة: 
- html5 و css3 و bootstrap و javascript 
- لا تستخدم react ولا nodejs 

تقسيم العمل والملفات: 
- يجب تقسيم العمل والملفات الى وحدات صغيرة منظمة يمكن التعامل معها وتطويرها وتعديلها بسهولة
- حجم الملفات والوحدات يجب ان يكون قابل للتعامل معه من خلال حدود استخدام ادوات الذكاء الصناعي للتعامل بسهولة مع كل وحدة 
- بعد الانتهاء راجع جميع الوظائف وتاكد ان عملية التوليد تتم بدون مشاكل 
- قسم الملفات لوحدات اصغر كلما واجهت مشكلة مثل:
I'm sorry. I tried to call a tool, but provided too large of an input. How would you like to proceed?


اتجاه الصفحة: 
- من اليمين لليسار rtl 

الخطوط: 
- خط cairo 
- خط tajawal 

التصميم: 
- تحسين التصميم العام: استخدام تصميم حديث مع ألوان متناسقة، ظلال ناعمة، وانتقالات (transitions) سلسة.
- تحسين تجربة المستخدم: إضافة تأثيرات تفاعلية مثل الـ hover والـ animations.
- يجب استخدام الوان وتصاميم واشكال وايقونات مناسبة لمجال وهدف الأداة او البرنامج
- بروجريس بار وتفاعلية احترافية مع المستخدم لتوضيح نسبة ومستوى التقدم في العملية وايقافها مؤقتا او الاستمرار بها ..الخ

السيو: 
- عنوان title كاتشي جذاب، متوافق مع معايير السيو الحديثة، لا يتعدى الـ 60 حرف ولا يقل عن 30 حرف
- الوصف ميتا ديسكريبشن كاتشي جذاب، متوافق مع معايير السيو الحديثة، لا يتعدى الـ 160 حرف ولا يقل عن 120 حرف 
- استخدام الكلمات المفتاحية الأساسية في العنوان والوصف 
- إضافة سكيما مناسبة schema مهم جدا (الاساسية للصفحة + faq .. الخ)
- إضافة كود كانونيكال اوتوماتيك يحدد الصفحة الحالية والرابط المفتوح الحالي كصفحة رئيسية
- إضافة مؤشرات الموثوقية EEAT بشكل احترافي 
- تحسين جميع تلك العوامل وال SEO ايضا لاي صفحة يتم توليدها او تصديرها

تضمين الذكاء الصناعي: 
استخدم gemini api 
يمكنك فهم طريقته من هنا:
API quickstart guide

curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=GEMINI_API_KEY" \
-H 'Content-Type: application/json' \
-X POST \
-d '{
  "contents": [{
    "parts":[{"text": "Explain how AI works"}]
    }]
   }'
قم بتدوير هذه ال apis بطريقة احترافية متقدمة من gemini لتخطي حدود التوكن ومشاكله 
كما يجب ادارته بحكمة عندما تكون الطلبات او عدد الكلمات المفتاحية كبير لكي لا تتجاوز حدود الطلب في المرة الواحدة من كل api
كما يجب تقسيم الصفحات والمحتوى الكبير الذي يتم ارفاقه الى اجزاء يمكن التعامل معها بطريقة احترافية ثم تجميعها وذلك للتغلب على حدود حجم المدخلات التي يتعامل معها كل  api:
Gemini API Key
AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ
AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg
AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw
AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak
AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc
AIzaSyBzlVUgjCkG44mm3_8Rt-ZiLskXsxM29SM
AIzaSyDeSqeSAb4QZ6JEf_LbLTrnVOgcGMSb7ds
AIzaSyAu5ToET8qHvDUPzrDOvkr1T99qZXvZORk
AIzaSyAFu5lHg2W5ibpFbm-k3kNlW-rI96JcQOg
AIzaSyBThniUP9oFxyriMOV4-LpvmJtW3u1gZCQ
AIzaSyAns8tVQ-V6sqbh7UPQB7tqYgWAZUIN6Z4
AIzaSyCS32gY-wcVTRIuE2PY_3ppVloGmvFlAqo
AIzaSyCc3Y4hHWuAxKtJx4E0h-spwYUP6Eqzk4k
AIzaSyAmxg1f8A3s1mXxdUav0zeyC52BF-5QBoA
AIzaSyD7C9Ry7EeCXwvrLkymXuUp3FwqbqloFuQ

شروط هامة:
- يجب أن لا يتم تكرار المقدمات في النتائج بدلاً من إظهار المحتوى الفعلي فقط. (يجب اظهار المحتوى الفعلي فقط)
- يجب ان يكون لكل مهمة برومبت احترافي جدا لكي يتم توليدها بأعلى معايير الجودة والإحترافية المناسبة 
- لا يتم كتابة اي تعليقات او توضيحات تبدو كانها من الذكاء الصناعي
- يجب الالتزام بالعدد المحدد المطلوب توليده من كل اختيار  
- اضف اي خيارات اخرى متقدمة واحترافية تجد انها ستساعد في جعل الاداة اكثر فعالية واحترافية 
- يجب وجود بروجريس لمتابعة التقدم 
- يجب ان تكون البيانات المولدة صحيحة ودقيقة 100% 
- يجب ان يكون عرض النتيجة منسق بالكامل واحترافي تظهر فيه القوائم والجداول منسقة وليس مجرد كود
- قم دائما بتقسيم الملفات لوحدات اصغر لكي تتجنب مشكلة:
I'm sorry. I tried to call a tool, but provided too large of an input. How would you like to proceed?


التصدير:
- ملف html منظم واحترافي جدا مع محرك بحث للبحث داخله عن المطلوب
- ملف اكسيل احترافي
- ملف تكست
- ملف pdf مع مراعاة اللغة العربية والتنسيق الاحترافي 
- ملف مضغوط يحتوي جميع الملفات والاقسام
- قم باضافة تصدير واستيراد للاعدادات والخيارات بحيث يمكن استخدام الاعدادات والخيارات مرة اخرى من خلال رفع الملف 
