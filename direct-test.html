<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مباشر - عرض النتائج</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: 'Cairo', sans-serif; padding: 2rem; }
        .test-section { margin: 2rem 0; padding: 1rem; border: 1px solid #ddd; border-radius: 8px; }
        .log { background: #f8f9fa; padding: 1rem; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .results-section { margin-top: 2rem; }
        .results-content { border: 2px solid #007bff; padding: 1rem; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار مباشر لعرض النتائج</h1>
        
        <div class="test-section">
            <h3>اختبار تنسيق المحتوى</h3>
            <button id="testFormatBtn" class="btn btn-primary">اختبار التنسيق</button>
            <div id="formatLog" class="log mt-2"></div>
        </div>

        <div class="test-section">
            <h3>اختبار عرض النتائج الكامل</h3>
            <button id="testFullDisplayBtn" class="btn btn-success">اختبار العرض الكامل</button>
            <div id="displayLog" class="log mt-2"></div>
        </div>

        <!-- قسم النتائج -->
        <section id="results" class="results-section">
            <div class="container">
                <h2>النتائج</h2>
                <div id="resultsContent" class="results-content">
                    <!-- Results will be displayed here -->
                </div>
            </div>
        </section>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/error-handler.js"></script>
    <script src="js/content-generator.js"></script>

    <script>
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            element.scrollTop = element.scrollHeight;
        }

        // اختبار تنسيق المحتوى
        document.getElementById('testFormatBtn').addEventListener('click', function() {
            const logId = 'formatLog';
            log(logId, 'بدء اختبار تنسيق المحتوى...');
            
            const testContent = {
                content: `# الإعجاز العلمي في الطب

## مقدمة
الطب في القرآن الكريم والسنة النبوية يحتوي على إعجاز علمي مذهل.

## الآيات القرآنية
قال تعالى: "وَنُنَزِّلُ مِنَ الْقُرْآنِ مَا هُوَ شِفَاءٌ وَرَحْمَةٌ لِّلْمُؤْمِنِينَ"

## الأحاديث النبوية
قال رسول الله صلى الله عليه وسلم: "ما أنزل الله داء إلا أنزل له شفاء"

## الحقائق العلمية
1. العسل كمضاد حيوي طبيعي
2. الحجامة وفوائدها الطبية
3. الصوم وتأثيره على الصحة

## الخلاصة
هذا مثال واضح على الإعجاز العلمي في الطب الإسلامي.`,
                topic: 'الطب',
                timestamp: new Date().toLocaleString('ar-SA'),
                searchId: 'direct-test-' + Date.now(),
                wordCount: 150,
                sections: 5
            };

            try {
                log(logId, 'المحتوى الأصلي:');
                log(logId, testContent.content.substring(0, 200) + '...');
                
                if (typeof contentGenerator !== 'undefined') {
                    log(logId, 'بدء تنسيق المحتوى...');
                    const formatted = contentGenerator.formatContent(testContent);
                    
                    log(logId, `تم التنسيق بنجاح! طول النتيجة: ${formatted.length} حرف`);
                    log(logId, 'أول 300 حرف من النتيجة:');
                    log(logId, formatted.substring(0, 300));
                    
                    // عرض النتيجة في قسم النتائج
                    const resultsContent = document.getElementById('resultsContent');
                    resultsContent.innerHTML = formatted;
                    
                    log(logId, '✅ تم عرض النتيجة في قسم النتائج');
                } else {
                    log(logId, '❌ contentGenerator غير متاح');
                }
                
            } catch (error) {
                log(logId, `❌ خطأ: ${error.message}`);
                console.error('خطأ في الاختبار:', error);
            }
        });

        // اختبار العرض الكامل
        document.getElementById('testFullDisplayBtn').addEventListener('click', function() {
            const logId = 'displayLog';
            log(logId, 'بدء اختبار العرض الكامل...');
            
            const testResults = {
                content: `# الإعجاز العلمي في الفضاء

## مقدمة
الفضاء في القرآن الكريم يحتوي على إشارات علمية دقيقة.

## الآيات القرآنية
قال تعالى: "وَالسَّمَاءَ بَنَيْنَاهَا بِأَيْدٍ وَإِنَّا لَمُوسِعُونَ"

## الحقائق العلمية
1. توسع الكون
2. البناء الكوني
3. النجوم والمجرات

## الخلاصة
إعجاز علمي واضح في وصف الكون.`,
                topic: 'الفضاء',
                timestamp: new Date().toLocaleString('ar-SA'),
                searchId: 'full-test-' + Date.now(),
                wordCount: 100,
                sections: 4
            };

            try {
                log(logId, 'فحص العناصر المطلوبة...');
                const resultsSection = document.getElementById('results');
                const resultsContent = document.getElementById('resultsContent');
                
                if (!resultsSection) {
                    log(logId, '❌ عنصر results غير موجود');
                    return;
                }
                if (!resultsContent) {
                    log(logId, '❌ عنصر resultsContent غير موجود');
                    return;
                }
                
                log(logId, '✅ العناصر موجودة');
                
                if (typeof contentGenerator !== 'undefined') {
                    log(logId, 'تنسيق المحتوى...');
                    const formattedContent = contentGenerator.formatContent(testResults);
                    log(logId, `تم التنسيق: ${formattedContent.length} حرف`);
                    
                    log(logId, 'إنشاء محرك البحث...');
                    const searchEngine = contentGenerator.createSearchEngine();
                    log(logId, `محرك البحث: ${searchEngine.length} حرف`);
                    
                    log(logId, 'دمج المحتوى...');
                    const finalContent = searchEngine + formattedContent;
                    log(logId, `المحتوى النهائي: ${finalContent.length} حرف`);
                    
                    log(logId, 'عرض المحتوى...');
                    resultsContent.innerHTML = finalContent;
                    
                    log(logId, '✅ تم العرض بنجاح!');
                    log(logId, `محتوى العنصر الآن: ${resultsContent.innerHTML.length} حرف`);
                    
                } else {
                    log(logId, '❌ contentGenerator غير متاح');
                }
                
            } catch (error) {
                log(logId, `❌ خطأ: ${error.message}`);
                console.error('خطأ في العرض:', error);
            }
        });

        // تشغيل اختبار أولي
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('testFormatBtn').click();
            }, 1000);
        });
    </script>
</body>
</html>
