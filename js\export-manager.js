// Export Manager Module - مكتبة الإعجاز العلمي

class ExportManager {
    constructor() {
        this.currentResults = null;
        this.exportTemplates = this.initializeExportTemplates();
    }

    // تهيئة قوالب التصدير
    initializeExportTemplates() {
        return {
            html: {
                header: `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}} - مكتبة الإعجاز العلمي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: 'Cairo', 'Tajawal', sans-serif; direction: rtl; text-align: right; }
        .header { background: linear-gradient(135deg, #2c5aa0, #1e3c72); color: white; padding: 2rem 0; }
        .content { padding: 2rem 0; }
        .verse-container, .hadith-container { margin: 1.5rem 0; padding: 1rem; border-right: 4px solid #2c5aa0; background: #f8f9fa; }
        .verse-text, .hadith-text { font-size: 1.1rem; font-weight: 500; color: #2c5aa0; }
        .search-info { background: #e9ecef; padding: 1rem; border-radius: 8px; margin: 2rem 0; }
        .section-title { color: #2c5aa0; border-bottom: 2px solid #2c5aa0; padding-bottom: 0.5rem; }
        .table { margin: 1rem 0; }
        .table th { background: #2c5aa0; color: white; }
        .faq-item { margin: 1rem 0; padding: 1rem; border: 1px solid #dee2e6; border-radius: 8px; }
        .faq-question { font-weight: 600; color: #2c5aa0; margin-bottom: 0.5rem; }
        .print-only { display: none; }
        @media print { .no-print { display: none; } .print-only { display: block; } }
        @page { margin: 2cm; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><i class="fas fa-book-open"></i> مكتبة الإعجاز العلمي</h1>
            <p class="lead">{{subtitle}}</p>
        </div>
    </div>
    <div class="container content">`,
                
                footer: `
        <div class="search-info print-only">
            <h5>معلومات التصدير</h5>
            <p><strong>تاريخ التصدير:</strong> {{exportDate}}</p>
            <p><strong>المصدر:</strong> مكتبة الإعجاز العلمي</p>
            <p><strong>الموقع:</strong> {{website}}</p>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // محرك البحث في الصفحة
        function searchInPage() {
            const query = document.getElementById('pageSearch').value.toLowerCase();
            const content = document.body.innerHTML;
            
            if (query) {
                const regex = new RegExp(query, 'gi');
                document.body.innerHTML = content.replace(regex, '<mark>$&</mark>');
            } else {
                document.body.innerHTML = content.replace(/<mark>/g, '').replace(/<\/mark>/g, '');
            }
        }
        
        // طباعة الصفحة
        function printPage() {
            window.print();
        }
    </script>
</body>
</html>`
            }
        };
    }

    // تصدير النتائج بصيغة HTML
    async exportToHTML(results, includeSearch = true) {
        if (!results) {
            throw new Error('لا توجد نتائج للتصدير');
        }

        const title = `الإعجاز العلمي في ${results.topic}`;
        const subtitle = `تحليل شامل ومفصل - ${results.timestamp}`;
        const exportDate = Utils.formatDate();
        const website = window.location.origin;

        // تنسيق المحتوى
        const formattedContent = contentGenerator.formatContent(results);

        // إضافة محرك البحث إذا كان مطلوباً
        let searchEngine = '';
        if (includeSearch) {
            searchEngine = `
                <div class="search-container no-print mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-search text-primary"></i>
                                البحث في المحتوى
                            </h5>
                            <div class="input-group">
                                <input type="text" id="pageSearch" class="form-control" placeholder="ابحث في المحتوى...">
                                <button class="btn btn-primary" onclick="searchInPage()">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <button class="btn btn-secondary" onclick="printPage()">
                                    <i class="fas fa-print"></i> طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // تجميع HTML النهائي
        let htmlContent = this.exportTemplates.html.header
            .replace('{{title}}', title)
            .replace('{{subtitle}}', subtitle);

        htmlContent += searchEngine;
        htmlContent += `<h1 class="text-center mb-4">${title}</h1>`;
        htmlContent += formattedContent;
        
        htmlContent += this.exportTemplates.html.footer
            .replace('{{exportDate}}', exportDate)
            .replace('{{website}}', website);

        return {
            content: htmlContent,
            filename: `الإعجاز_العلمي_${results.topic}_${Date.now()}.html`,
            mimeType: 'text/html'
        };
    }

    // تصدير النتائج بصيغة PDF
    async exportToPDF(results) {
        if (!results) {
            throw new Error('لا توجد نتائج للتصدير');
        }

        // إنشاء HTML مؤقت للتحويل إلى PDF
        const htmlExport = await this.exportToHTML(results, false);
        
        // إنشاء عنصر مؤقت لطباعة PDF
        const printWindow = window.open('', '_blank');
        printWindow.document.write(htmlExport.content);
        printWindow.document.close();
        
        // تأخير قصير للسماح بتحميل المحتوى
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 1000);

        return {
            success: true,
            message: 'تم فتح نافذة الطباعة. يمكنك حفظ الملف كـ PDF من خيارات الطباعة.'
        };
    }

    // تصدير النتائج بصيغة Excel
    async exportToExcel(results) {
        if (!results) {
            throw new Error('لا توجد نتائج للتصدير');
        }

        // إنشاء بيانات Excel
        const workbookData = this.prepareExcelData(results);
        
        // تحويل إلى CSV (بديل بسيط لـ Excel)
        let csvContent = '\uFEFF'; // BOM for UTF-8
        
        // إضافة معلومات أساسية
        csvContent += `الموضوع,${results.topic}\n`;
        csvContent += `تاريخ البحث,${results.timestamp}\n`;
        csvContent += `معرف البحث,${results.searchId}\n`;
        csvContent += `عدد الكلمات,${results.wordCount || 'غير محدد'}\n\n`;

        // إضافة المحتوى الرئيسي
        csvContent += 'القسم,المحتوى\n';
        
        if (results.results && Array.isArray(results.results)) {
            results.results.forEach(result => {
                const cleanContent = this.cleanTextForCSV(result.content);
                csvContent += `"${result.title}","${cleanContent}"\n`;
            });
        }

        return {
            content: csvContent,
            filename: `الإعجاز_العلمي_${results.topic}_${Date.now()}.csv`,
            mimeType: 'text/csv;charset=utf-8'
        };
    }

    // تصدير النتائج بصيغة نص
    async exportToText(results) {
        if (!results) {
            throw new Error('لا توجد نتائج للتصدير');
        }

        let textContent = '';
        
        // إضافة رأس الملف
        textContent += '='.repeat(60) + '\n';
        textContent += `مكتبة الإعجاز العلمي\n`;
        textContent += `الإعجاز العلمي في ${results.topic}\n`;
        textContent += '='.repeat(60) + '\n\n';

        // معلومات البحث
        textContent += `تاريخ البحث: ${results.timestamp}\n`;
        textContent += `معرف البحث: ${results.searchId}\n`;
        textContent += `عدد الكلمات: ${results.wordCount || 'غير محدد'}\n`;
        textContent += `عدد الأقسام: ${results.sections || 'غير محدد'}\n\n`;

        textContent += '-'.repeat(60) + '\n\n';

        // المحتوى الرئيسي
        if (results.content) {
            textContent += this.cleanTextForTXT(results.content);
        }

        // إضافة ذيل الملف
        textContent += '\n\n' + '-'.repeat(60) + '\n';
        textContent += `تم التصدير في: ${Utils.formatDate()}\n`;
        textContent += 'المصدر: مكتبة الإعجاز العلمي\n';

        return {
            content: textContent,
            filename: `الإعجاز_العلمي_${results.topic}_${Date.now()}.txt`,
            mimeType: 'text/plain;charset=utf-8'
        };
    }

    // تصدير النتائج كملف مضغوط
    async exportToZip(results) {
        if (!results) {
            throw new Error('لا توجد نتائج للتصدير');
        }

        // إنشاء الملفات المختلفة
        const htmlExport = await this.exportToHTML(results);
        const textExport = await this.exportToText(results);
        const csvExport = await this.exportToExcel(results);

        // إنشاء ملف معلومات إضافي
        const infoContent = this.createInfoFile(results);

        // في بيئة المتصفح، نحتاج لمكتبة خارجية لإنشاء ZIP
        // هنا سنقوم بإنشاء رابط تحميل لكل ملف منفصل
        const files = [
            { ...htmlExport, description: 'ملف HTML تفاعلي' },
            { ...textExport, description: 'ملف نصي بسيط' },
            { ...csvExport, description: 'ملف بيانات CSV' },
            { 
                content: infoContent, 
                filename: `معلومات_البحث_${Date.now()}.txt`,
                mimeType: 'text/plain;charset=utf-8',
                description: 'ملف معلومات البحث'
            }
        ];

        return {
            files: files,
            message: 'سيتم تحميل الملفات منفصلة. يمكنك ضغطها يدوياً إذا أردت.'
        };
    }

    // إنشاء ملف معلومات
    createInfoFile(results) {
        let info = 'معلومات البحث - مكتبة الإعجاز العلمي\n';
        info += '='.repeat(50) + '\n\n';
        
        info += `الموضوع: ${results.topic}\n`;
        info += `تاريخ البحث: ${results.timestamp}\n`;
        info += `معرف البحث: ${results.searchId}\n`;
        info += `عدد الكلمات: ${results.wordCount || 'غير محدد'}\n`;
        info += `عدد الأقسام: ${results.sections || 'غير محدد'}\n\n`;

        if (results.settings) {
            info += 'إعدادات البحث:\n';
            info += `-----------------\n`;
            info += `مستوى التفصيل: ${results.settings.detailLevel || 'غير محدد'}\n`;
            info += `نوع المصادر: ${results.settings.sourceType || 'غير محدد'}\n`;
            info += `تضمين الجداول: ${results.settings.includeTables ? 'نعم' : 'لا'}\n`;
            info += `تضمين الأسئلة الشائعة: ${results.settings.includeFAQ ? 'نعم' : 'لا'}\n\n`;
        }

        info += `تاريخ التصدير: ${Utils.formatDate()}\n`;
        info += 'المصدر: مكتبة الإعجاز العلمي\n';
        info += 'جميع الحقوق محفوظة © 2024\n';

        return info;
    }

    // تنظيف النص لـ CSV
    cleanTextForCSV(text) {
        return text
            .replace(/"/g, '""') // escape quotes
            .replace(/\n/g, ' ') // remove line breaks
            .replace(/\s+/g, ' ') // normalize spaces
            .trim();
    }

    // تنظيف النص لـ TXT
    cleanTextForTXT(text) {
        return text
            .replace(/<[^>]*>/g, '') // remove HTML tags
            .replace(/&nbsp;/g, ' ') // replace HTML entities
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/\s+/g, ' ') // normalize spaces
            .replace(/\n\s*\n/g, '\n\n') // normalize line breaks
            .trim();
    }

    // إعداد بيانات Excel
    prepareExcelData(results) {
        const data = {
            info: {
                topic: results.topic,
                timestamp: results.timestamp,
                searchId: results.searchId,
                wordCount: results.wordCount,
                sections: results.sections
            },
            content: []
        };

        if (results.results && Array.isArray(results.results)) {
            results.results.forEach(result => {
                data.content.push({
                    title: result.title,
                    content: this.cleanTextForCSV(result.content),
                    type: result.type
                });
            });
        }

        return data;
    }

    // تحميل ملف
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // تنظيف الذاكرة
        setTimeout(() => URL.revokeObjectURL(url), 1000);
    }

    // تصدير الإعدادات
    exportSettings() {
        const settings = Utils.loadSettings();
        const exportData = {
            settings: settings,
            exportDate: Utils.formatDate(),
            version: '1.0',
            source: 'مكتبة الإعجاز العلمي'
        };

        const content = JSON.stringify(exportData, null, 2);
        this.downloadFile(
            content,
            `إعدادات_مكتبة_الإعجاز_${Date.now()}.json`,
            'application/json'
        );
    }

    // استيراد الإعدادات
    importSettings(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    
                    if (data.settings) {
                        Utils.saveSettings(data.settings);
                        
                        // تطبيق الإعدادات على النموذج
                        this.applySettingsToForm(data.settings);
                        
                        resolve({
                            success: true,
                            message: 'تم استيراد الإعدادات بنجاح',
                            settings: data.settings
                        });
                    } else {
                        reject(new Error('ملف الإعدادات غير صالح'));
                    }
                } catch (error) {
                    reject(new Error('خطأ في قراءة ملف الإعدادات: ' + error.message));
                }
            };
            
            reader.onerror = () => {
                reject(new Error('خطأ في قراءة الملف'));
            };
            
            reader.readAsText(file);
        });
    }

    // تطبيق الإعدادات على النموذج
    applySettingsToForm(settings) {
        if (settings.detailLevel) {
            const detailSelect = document.getElementById('detailLevel');
            if (detailSelect) detailSelect.value = settings.detailLevel;
        }

        if (settings.sourceType) {
            const sourceSelect = document.getElementById('sourceType');
            if (sourceSelect) sourceSelect.value = settings.sourceType;
        }

        if (typeof settings.includeTables === 'boolean') {
            const tablesCheck = document.getElementById('includeTables');
            if (tablesCheck) tablesCheck.checked = settings.includeTables;
        }

        if (typeof settings.includeFAQ === 'boolean') {
            const faqCheck = document.getElementById('includeFAQ');
            if (faqCheck) faqCheck.checked = settings.includeFAQ;
        }
    }

    // تعيين النتائج الحالية
    setCurrentResults(results) {
        this.currentResults = results;
    }

    // الحصول على النتائج الحالية
    getCurrentResults() {
        return this.currentResults;
    }
}

// إنشاء مثيل من مدير التصدير
const exportManager = new ExportManager();

// تصدير للاستخدام في الملفات الأخرى
window.ExportManager = ExportManager;
window.exportManager = exportManager;

console.log('تم تحميل وحدة مدير التصدير بنجاح');
