// Configuration File - مكتبة الإعجاز العلمي

// Gemini API Configuration
const GEMINI_API_KEYS = [
    'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
    'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
    'AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw',
    'AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak',
    'AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc',
    'AIzaSyBzlVUgjCkG44mm3_8Rt-ZiLskXsxM29SM',
    'AIzaSyDeSqeSAb4QZ6JEf_LbLTrnVOgcGMSb7ds',
    'AIzaSyAu5ToET8qHvDUPzrDOvkr1T99qZXvZORk',
    'AIzaSyAFu5lHg2W5ibpFbm-k3kNlW-rI96JcQOg',
    'AIzaSyBThniUP9oFxyriMOV4-LpvmJtW3u1gZCQ',
    'AIzaSyAns8tVQ-V6sqbh7UPQB7tqYgWAZUIN6Z4',
    'AIzaSyCS32gY-wcVTRIuE2PY_3ppVloGmvFlAqo',
    'AIzaSyCc3Y4hHWuAxKtJx4E0h-spwYUP6Eqzk4k',
    'AIzaSyAmxg1f8A3s1mXxdUav0zeyC52BF-5QBoA',
    'AIzaSyD7C9Ry7EeCXwvrLkymXuUp3FwqbqloFuQ'
];

const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

// API Management
class APIManager {
    constructor() {
        this.currentKeyIndex = 0;
        this.keyUsageCount = new Array(GEMINI_API_KEYS.length).fill(0);
        this.maxRequestsPerKey = 50; // تحديد عدد الطلبات لكل مفتاح
        this.requestDelay = 1000; // تأخير بين الطلبات بالميلي ثانية
        this.lastRequestTime = 0;
    }

    // الحصول على المفتاح التالي المتاح
    getNextAvailableKey() {
        console.log('البحث عن مفتاح API متاح...');

        // البحث عن مفتاح لم يتم استنفاذه
        for (let i = 0; i < GEMINI_API_KEYS.length; i++) {
            const keyIndex = (this.currentKeyIndex + i) % GEMINI_API_KEYS.length;
            const usageCount = this.keyUsageCount[keyIndex];

            console.log(`فحص المفتاح ${keyIndex}: استخدم ${usageCount}/${this.maxRequestsPerKey} مرة`);

            if (usageCount < this.maxRequestsPerKey) {
                this.currentKeyIndex = keyIndex;
                const key = GEMINI_API_KEYS[keyIndex];
                console.log(`تم اختيار المفتاح ${keyIndex}: ${key.substring(0, 10)}...`);
                return key;
            }
        }

        // إذا تم استنفاذ جميع المفاتيح، إعادة تعيين العدادات
        console.log('تم استنفاذ جميع المفاتيح، إعادة تعيين العدادات...');
        this.resetUsageCounters();
        this.currentKeyIndex = 0;
        const key = GEMINI_API_KEYS[0];
        console.log(`استخدام المفتاح الأول بعد إعادة التعيين: ${key.substring(0, 10)}...`);
        return key;
    }

    // إعادة تعيين عدادات الاستخدام
    resetUsageCounters() {
        this.keyUsageCount.fill(0);
        console.log('تم إعادة تعيين عدادات استخدام المفاتيح');
    }

    // تسجيل استخدام مفتاح
    recordKeyUsage() {
        this.keyUsageCount[this.currentKeyIndex]++;
    }

    // التحقق من التأخير المطلوب
    async enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        
        if (timeSinceLastRequest < this.requestDelay) {
            const waitTime = this.requestDelay - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        
        this.lastRequestTime = Date.now();
    }

    // اختبار صحة مفتاح API
    async testAPIKey(apiKey) {
        const url = `${GEMINI_API_URL}?key=${apiKey}`;
        const testPrompt = 'اكتب كلمة واحدة: مرحبا';

        const requestBody = {
            contents: [{
                parts: [{ text: testPrompt }]
            }],
            generationConfig: {
                temperature: 0.1,
                maxOutputTokens: 10,
            }
        };

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            return response.ok;
        } catch (error) {
            console.error('خطأ في اختبار المفتاح:', error);
            return false;
        }
    }

    // اختبار جميع مفاتيح API
    async testAllKeys() {
        console.log('بدء اختبار جميع مفاتيح API...');
        const results = [];

        for (let i = 0; i < GEMINI_API_KEYS.length; i++) {
            const key = GEMINI_API_KEYS[i];
            console.log(`اختبار المفتاح ${i + 1}/${GEMINI_API_KEYS.length}...`);

            const isValid = await this.testAPIKey(key);
            results.push({
                index: i,
                key: key.substring(0, 10) + '...',
                valid: isValid
            });

            // تأخير قصير بين الاختبارات
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        const validKeys = results.filter(r => r.valid).length;
        console.log(`تم اختبار ${GEMINI_API_KEYS.length} مفتاح، ${validKeys} صالح`);

        return results;
    }

    // الحصول على إحصائيات الاستخدام
    getUsageStats() {
        return {
            currentKey: this.currentKeyIndex,
            totalKeys: GEMINI_API_KEYS.length,
            keyUsage: this.keyUsageCount,
            availableKeys: this.keyUsageCount.filter(count => count < this.maxRequestsPerKey).length
        };
    }
}

// إنشاء مثيل من مدير API
const apiManager = new APIManager();

// إعدادات التطبيق الافتراضية
const DEFAULT_SETTINGS = {
    detailLevel: 'intermediate',
    sourceType: 'all',
    includeTables: true,
    includeFAQ: true,
    language: 'ar',
    maxTokensPerRequest: 4000,
    chunkSize: 2000,
    exportFormat: 'html'
};

// إعدادات المحتوى
const CONTENT_SETTINGS = {
    maxContentLength: 50000, // الحد الأقصى لطول المحتوى
    chunkOverlap: 200, // التداخل بين الأجزاء
    minChunkSize: 1000, // الحد الأدنى لحجم الجزء
    maxRetries: 3, // عدد المحاولات القصوى
    retryDelay: 2000 // تأخير إعادة المحاولة
};

// رسائل النظام للذكاء الاصطناعي
const SYSTEM_PROMPTS = {
    main: `أنت خبير في الإعجاز العلمي في القرآن الكريم والسنة النبوية الشريفة. مهمتك هي تحليل المجالات العلمية وإظهار الإعجاز العلمي فيها بطريقة دقيقة وموثقة.

يجب أن تتضمن إجابتك:
1. الآيات القرآنية ذات الصلة مع التفسير
2. الأحاديث النبوية الصحيحة المرتبطة بالموضوع
3. الحقائق العلمية الحديثة المؤكدة
4. تحليل أوجه الإعجاز والتطابق
5. الرد على الاعتراضات المحتملة
6. المصادر العلمية والشرعية الموثقة

يجب أن تكون المعلومات دقيقة 100% ومن مصادر موثقة فقط.`,

    detailed: `قم بإنشاء تحليل مفصل وشامل للإعجاز العلمي في الموضوع المحدد. يجب أن يتضمن التحليل:

1. **المقدمة العلمية**: نبذة عن المجال العلمي وأهميته
2. **النصوص الشرعية**: الآيات والأحاديث ذات الصلة
3. **التفسير والشرح**: شرح مفصل للنصوص وسياقها
4. **الحقائق العلمية**: الاكتشافات والحقائق العلمية المؤكدة
5. **أوجه الإعجاز**: تحليل التطابق والسبق العلمي
6. **المقارنات**: مقارنة مع التفسيرات القديمة
7. **الاعتراضات والردود**: مناقشة الاعتراضات وتفنيدها
8. **الأسئلة الشائعة**: الأسئلة المتكررة وإجاباتها
9. **المصادر**: قائمة بالمصادر العلمية والشرعية

استخدم لغة علمية دقيقة ومفهومة، وتجنب التأويلات المتكلفة.`,

    summary: `قم بإنشاء ملخص موجز ومركز للإعجاز العلمي في الموضوع المحدد. يجب أن يتضمن:

1. النقاط الرئيسية للإعجاز
2. أهم الآيات والأحاديث
3. الحقائق العلمية الأساسية
4. خلاصة أوجه التطابق

اجعل الملخص واضحاً ومفيداً للقارئ العام.`
};

// إعدادات التصدير
const EXPORT_SETTINGS = {
    html: {
        template: 'modern',
        includeCSS: true,
        includeSearch: true,
        rtl: true
    },
    pdf: {
        format: 'A4',
        orientation: 'portrait',
        margin: '20mm',
        rtl: true,
        font: 'Cairo'
    },
    excel: {
        sheetName: 'الإعجاز العلمي',
        includeCharts: true,
        rtl: true
    },
    txt: {
        encoding: 'utf-8',
        lineBreak: '\n',
        rtl: true
    }
};

// دوال المساعدة
const Utils = {
    // تقسيم النص إلى أجزاء
    chunkText: function(text, maxLength = CONTENT_SETTINGS.maxContentLength) {
        if (text.length <= maxLength) return [text];
        
        const chunks = [];
        let start = 0;
        
        while (start < text.length) {
            let end = start + maxLength;
            
            // البحث عن نقطة قطع مناسبة (نهاية جملة)
            if (end < text.length) {
                const lastPeriod = text.lastIndexOf('.', end);
                const lastNewline = text.lastIndexOf('\n', end);
                const cutPoint = Math.max(lastPeriod, lastNewline);
                
                if (cutPoint > start + CONTENT_SETTINGS.minChunkSize) {
                    end = cutPoint + 1;
                }
            }
            
            chunks.push(text.substring(start, end));
            start = end - CONTENT_SETTINGS.chunkOverlap;
        }
        
        return chunks;
    },

    // تنظيف النص
    cleanText: function(text) {
        return text
            .replace(/\s+/g, ' ')
            .replace(/\n\s*\n/g, '\n\n')
            .trim();
    },

    // تنسيق التاريخ
    formatDate: function(date = new Date()) {
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    // إنشاء معرف فريد
    generateId: function() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    // حفظ الإعدادات في التخزين المحلي
    saveSettings: function(settings) {
        try {
            localStorage.setItem('scientificMiraclesSettings', JSON.stringify(settings));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    },

    // تحميل الإعدادات من التخزين المحلي
    loadSettings: function() {
        try {
            const saved = localStorage.getItem('scientificMiraclesSettings');
            return saved ? JSON.parse(saved) : DEFAULT_SETTINGS;
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
            return DEFAULT_SETTINGS;
        }
    },

    // تحديث الرابط الكانونيكال
    updateCanonicalUrl: function() {
        const canonical = document.querySelector('link[rel="canonical"]');
        if (canonical) {
            canonical.href = window.location.href;
        }
    },

    // تحديث البيانات المنظمة
    updateStructuredData: function(data) {
        const script = document.querySelector('script[type="application/ld+json"]');
        if (script && data) {
            const structuredData = JSON.parse(script.textContent);
            Object.assign(structuredData, data);
            script.textContent = JSON.stringify(structuredData, null, 2);
        }
    }
};

// تصدير المتغيرات والدوال للاستخدام في الملفات الأخرى
window.GEMINI_API_KEYS = GEMINI_API_KEYS;
window.GEMINI_API_URL = GEMINI_API_URL;
window.apiManager = apiManager;
window.DEFAULT_SETTINGS = DEFAULT_SETTINGS;
window.CONTENT_SETTINGS = CONTENT_SETTINGS;
window.SYSTEM_PROMPTS = SYSTEM_PROMPTS;
window.EXPORT_SETTINGS = EXPORT_SETTINGS;
window.Utils = Utils;

// تحديث الرابط الكانونيكال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    Utils.updateCanonicalUrl();
});

console.log('تم تحميل ملف الإعدادات بنجاح');
