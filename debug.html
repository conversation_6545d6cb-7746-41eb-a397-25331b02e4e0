<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص سريع - مكتبة الإعجاز العلمي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; padding: 2rem; }
        .debug-section { margin: 2rem 0; padding: 1rem; border: 1px solid #ddd; border-radius: 8px; }
        .log { background: #f8f9fa; padding: 1rem; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>تشخيص سريع للمشكلة</h1>
        
        <div class="debug-section">
            <h3>1. اختبار النتائج المحفوظة</h3>
            <button id="checkSavedBtn" class="btn btn-primary">فحص النتائج المحفوظة</button>
            <div id="savedResults" class="log mt-2"></div>
        </div>

        <div class="debug-section">
            <h3>2. إنشاء نتائج تجريبية</h3>
            <button id="createTestBtn" class="btn btn-success">إنشاء نتائج تجريبية</button>
            <div id="testResults" class="log mt-2"></div>
        </div>

        <div class="debug-section">
            <h3>3. اختبار عرض النتائج</h3>
            <button id="testDisplayBtn" class="btn btn-warning">اختبار العرض</button>
            <div id="displayTest" class="log mt-2"></div>
        </div>

        <div class="debug-section">
            <h3>4. فحص العناصر</h3>
            <button id="checkElementsBtn" class="btn btn-info">فحص العناصر</button>
            <div id="elementsCheck" class="log mt-2"></div>
        </div>

        <!-- قسم النتائج المخفي -->
        <section id="results" class="results-section d-none">
            <div class="container">
                <h2>نتائج الاختبار</h2>
                <div id="resultsContent" class="results-content">
                    <!-- Results will be dynamically inserted here -->
                </div>
            </div>
        </section>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/error-handler.js"></script>
    <script src="js/content-generator.js"></script>
    <script src="js/export-manager.js"></script>

    <script>
        // دالة لتسجيل الرسائل
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }

        // فحص النتائج المحفوظة
        document.getElementById('checkSavedBtn').addEventListener('click', function() {
            const logElement = 'savedResults';
            log(logElement, 'بدء فحص النتائج المحفوظة...');
            
            try {
                // فحص جميع المفاتيح المحتملة
                const keys = ['currentResults', 'scientificMiraclesResults'];
                let found = false;
                
                keys.forEach(key => {
                    const data = localStorage.getItem(key);
                    if (data) {
                        found = true;
                        const parsed = JSON.parse(data);
                        log(logElement, `✅ تم العثور على بيانات في ${key}`);
                        log(logElement, `   الموضوع: ${parsed.topic || 'غير محدد'}`);
                        log(logElement, `   طول المحتوى: ${parsed.content ? parsed.content.length : 0} حرف`);
                        log(logElement, `   التاريخ: ${parsed.timestamp || 'غير محدد'}`);
                    } else {
                        log(logElement, `❌ لا توجد بيانات في ${key}`);
                    }
                });
                
                if (!found) {
                    log(logElement, '⚠️ لم يتم العثور على أي نتائج محفوظة');
                }
                
            } catch (error) {
                log(logElement, `❌ خطأ: ${error.message}`);
            }
        });

        // إنشاء نتائج تجريبية
        document.getElementById('createTestBtn').addEventListener('click', function() {
            const logElement = 'testResults';
            log(logElement, 'إنشاء نتائج تجريبية...');
            
            try {
                const testData = {
                    content: `# الإعجاز العلمي في الفضاء

## مقدمة
هذا محتوى تجريبي لاختبار عرض النتائج.

## الآيات القرآنية
قال تعالى: "وَالسَّمَاءَ بَنَيْنَاهَا بِأَيْدٍ وَإِنَّا لَمُوسِعُونَ"

## الحقائق العلمية
1. توسع الكون
2. البناء الكوني
3. القوى الفيزيائية

## الخلاصة
هذا مثال على النتائج المنسقة.`,
                    topic: 'الفضاء',
                    timestamp: new Date().toLocaleString('ar-SA'),
                    searchId: 'debug-test-' + Date.now(),
                    wordCount: 100,
                    sections: 4
                };

                // حفظ في التخزين المحلي
                localStorage.setItem('scientificMiraclesResults', JSON.stringify(testData));
                localStorage.setItem('currentResults', JSON.stringify(testData));
                
                log(logElement, '✅ تم إنشاء وحفظ النتائج التجريبية');
                log(logElement, `   الموضوع: ${testData.topic}`);
                log(logElement, `   طول المحتوى: ${testData.content.length} حرف`);
                
            } catch (error) {
                log(logElement, `❌ خطأ: ${error.message}`);
            }
        });

        // اختبار عرض النتائج
        document.getElementById('testDisplayBtn').addEventListener('click', function() {
            const logElement = 'displayTest';
            log(logElement, 'اختبار عرض النتائج...');
            
            try {
                // تحميل النتائج
                const data = localStorage.getItem('scientificMiraclesResults');
                if (!data) {
                    log(logElement, '❌ لا توجد نتائج للاختبار - أنشئ نتائج تجريبية أولاً');
                    return;
                }
                
                const results = JSON.parse(data);
                log(logElement, '✅ تم تحميل النتائج');
                
                // اختبار تنسيق المحتوى
                if (typeof contentGenerator !== 'undefined') {
                    const formatted = contentGenerator.formatContent(results);
                    log(logElement, `✅ تم تنسيق المحتوى (${formatted.length} حرف)`);
                    
                    // عرض النتائج
                    const resultsSection = document.getElementById('results');
                    const resultsContent = document.getElementById('resultsContent');
                    
                    if (resultsSection && resultsContent) {
                        resultsContent.innerHTML = formatted;
                        resultsSection.classList.remove('d-none');
                        log(logElement, '✅ تم عرض النتائج في الصفحة');
                    } else {
                        log(logElement, '❌ عناصر العرض غير موجودة');
                    }
                } else {
                    log(logElement, '❌ contentGenerator غير متاح');
                }
                
            } catch (error) {
                log(logElement, `❌ خطأ: ${error.message}`);
            }
        });

        // فحص العناصر
        document.getElementById('checkElementsBtn').addEventListener('click', function() {
            const logElement = 'elementsCheck';
            log(logElement, 'فحص العناصر المطلوبة...');
            
            const elements = [
                'results',
                'resultsContent'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    log(logElement, `✅ العنصر ${id} موجود`);
                    log(logElement, `   الفئات: ${element.className}`);
                } else {
                    log(logElement, `❌ العنصر ${id} غير موجود`);
                }
            });
            
            // فحص الوحدات المحملة
            const modules = [
                'contentGenerator',
                'exportManager',
                'errorHandler'
            ];
            
            modules.forEach(module => {
                if (typeof window[module] !== 'undefined') {
                    log(logElement, `✅ الوحدة ${module} محملة`);
                } else {
                    log(logElement, `❌ الوحدة ${module} غير محملة`);
                }
            });
        });

        // تشغيل فحص أولي
        window.addEventListener('load', function() {
            document.getElementById('checkElementsBtn').click();
        });
    </script>
</body>
</html>
