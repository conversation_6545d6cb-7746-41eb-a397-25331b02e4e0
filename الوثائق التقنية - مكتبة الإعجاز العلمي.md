# الوثائق التقنية - مكتبة الإعجاز العلمي

## نظرة عامة تقنية

هذا المستند يوفر دليلاً تقنياً شاملاً لمطوري ومهندسي البرمجيات الذين يرغبون في فهم البنية التقنية للمشروع أو المساهمة في تطويره.

## البنية المعمارية

### نمط التصميم
يتبع المشروع نمط **Module Pattern** مع **Separation of Concerns**:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Presentation  │    │   Business      │    │   Data Access   │
│   Layer         │    │   Logic Layer   │    │   Layer         │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ - HTML/CSS      │    │ - AI Integration│    │ - Gemini API    │
│ - UI Components │    │ - Content Gen.  │    │ - Local Storage │
│ - Event Handlers│    │ - Export Mgmt.  │    │ - Settings      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### مكونات النظام

#### 1. طبقة العرض (Presentation Layer)
- **HTML**: هيكل الصفحة مع دعم RTL
- **CSS**: تصميم متجاوب مع Bootstrap
- **JavaScript**: تفاعل المستخدم والتحكم في الواجهة

#### 2. طبقة المنطق التجاري (Business Logic Layer)
- **AI Integration**: إدارة التكامل مع Gemini API
- **Content Generator**: تنسيق وعرض المحتوى
- **Export Manager**: إدارة عمليات التصدير

#### 3. طبقة الوصول للبيانات (Data Access Layer)
- **API Manager**: إدارة مفاتيح API والطلبات
- **Local Storage**: حفظ الإعدادات والنتائج
- **Settings Manager**: إدارة إعدادات التطبيق

## تفاصيل الملفات التقنية

### 1. config.js - ملف الإعدادات

```javascript
// هيكل الإعدادات الرئيسية
const GEMINI_API_KEYS = []; // مصفوفة مفاتيح API
const DEFAULT_SETTINGS = {}; // الإعدادات الافتراضية
const CONTENT_SETTINGS = {}; // إعدادات المحتوى
const SYSTEM_PROMPTS = {};   // رسائل النظام للذكاء الاصطناعي
```

**الوظائف الرئيسية**:
- `APIManager`: إدارة مفاتيح API مع التدوير الذكي
- `Utils`: دوال مساعدة للتخزين والتنسيق
- إعدادات التصدير والقوالب

### 2. ai-integration.js - تكامل الذكاء الاصطناعي

```javascript
class AIIntegration {
    constructor() {
        this.isProcessing = false;
        this.currentRequest = null;
        this.progressCallback = null;
        this.isPaused = false;
        this.isStopped = false;
    }
}
```

**الميزات التقنية**:
- **Rate Limiting**: تحكم في معدل الطلبات
- **Request Chunking**: تقسيم المحتوى الكبير
- **Error Recovery**: إعادة المحاولة التلقائية
- **Progress Tracking**: تتبع التقدم في الوقت الفعلي
- **Pause/Resume**: إيقاف واستئناف العمليات

### 3. content-generator.js - مولد المحتوى

```javascript
class ContentGenerator {
    constructor() {
        this.currentResults = null;
        this.searchHistory = [];
        this.templates = this.initializeTemplates();
    }
}
```

**القوالب المدعومة**:
- `verse`: قالب الآيات القرآنية
- `hadith`: قالب الأحاديث النبوية
- `scientificFact`: قالب الحقائق العلمية
- `comparison`: قالب المقارنات
- `table`: قالب الجداول
- `faq`: قالب الأسئلة الشائعة

### 4. export-manager.js - مدير التصدير

```javascript
class ExportManager {
    constructor() {
        this.currentResults = null;
        this.exportTemplates = this.initializeExportTemplates();
    }
}
```

**صيغ التصدير المدعومة**:
- **HTML**: ملف تفاعلي مع CSS مدمج
- **PDF**: عبر طباعة المتصفح
- **CSV/Excel**: بيانات منظمة
- **TXT**: نص بسيط
- **ZIP**: مجموعة ملفات مضغوطة

## API Integration - تكامل واجهة البرمجة

### Gemini API Configuration

```javascript
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

// هيكل الطلب
const requestBody = {
    contents: [{
        parts: [{ text: prompt }]
    }],
    generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 4000,
    },
    safetySettings: [...]
};
```

### إدارة المفاتيح (API Key Management)

```javascript
class APIManager {
    getNextAvailableKey() {
        // خوارزمية التدوير الذكي
        // تتبع استخدام كل مفتاح
        // إعادة تعيين العدادات عند الحاجة
    }
    
    enforceRateLimit() {
        // تطبيق حدود معدل الطلبات
        // تأخير بين الطلبات
    }
}
```

### معالجة الأخطاء

```javascript
// أنواع الأخطاء المدعومة
- 429: تجاوز حد الطلبات
- 400: خطأ في تنسيق الطلب
- 403: مفتاح API غير صالح
- Network errors: أخطاء الشبكة
```

## تحسين الأداء (Performance Optimization)

### 1. تحميل الموارد
```html
<!-- تحميل الخطوط مسبقاً -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- تحميل CSS الحرج أولاً -->
<link rel="stylesheet" href="css/main.css">
```

### 2. تحسين JavaScript
```javascript
// استخدام debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
```

### 3. إدارة الذاكرة
```javascript
// تنظيف الذاكرة بعد التصدير
setTimeout(() => URL.revokeObjectURL(url), 1000);

// حد أقصى لتاريخ البحث
if (this.searchHistory.length > 10) {
    this.searchHistory = this.searchHistory.slice(0, 10);
}
```

## الأمان (Security)

### 1. حماية API Keys
```javascript
// المفاتيح محفوظة في متغيرات محلية
// عدم تسريب المفاتيح في console.log
// استخدام HTTPS فقط
```

### 2. تنظيف المدخلات
```javascript
// تنظيف النصوص من HTML
cleanTextForCSV(text) {
    return text
        .replace(/"/g, '""')
        .replace(/\n/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();
}
```

### 3. Content Security Policy
```html
<!-- يُنصح بإضافة CSP headers -->
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; 
               style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;">
```

## اختبار الجودة (Quality Assurance)

### 1. اختبارات الوحدة
```javascript
// اختبار دوال المساعدة
console.assert(Utils.chunkText('test').length === 1);
console.assert(apiManager.getUsageStats().totalKeys === 15);
```

### 2. اختبارات التكامل
```javascript
// اختبار تدفق البحث الكامل
async function testSearchFlow() {
    const results = await aiIntegration.processLargeContent('test', settings);
    console.assert(results.content.length > 0);
}
```

### 3. اختبارات الأداء
```javascript
// قياس وقت الاستجابة
const startTime = performance.now();
await sendGeminiRequest(prompt);
const endTime = performance.now();
console.log(`Request took ${endTime - startTime} milliseconds`);
```

## إرشادات التطوير

### 1. معايير الكود
```javascript
// استخدام const/let بدلاً من var
const API_URL = 'https://...';
let currentResults = null;

// أسماء وصفية للمتغيرات والدوال
function processLargeContent(topic, settings) { }

// تعليقات باللغة العربية
// هذه الدالة تقوم بمعالجة المحتوى الكبير
```

### 2. معالجة الأخطاء
```javascript
try {
    const result = await apiCall();
    return result;
} catch (error) {
    console.error('خطأ في استدعاء API:', error);
    throw new Error(`فشل في العملية: ${error.message}`);
}
```

### 3. التوثيق
```javascript
/**
 * معالجة المحتوى الكبير بتقسيمه إلى أجزاء
 * @param {string} topic - الموضوع المراد تحليله
 * @param {Object} settings - إعدادات البحث
 * @returns {Promise<Object>} النتائج المعالجة
 */
async function processLargeContent(topic, settings) { }
```

## نشر التطبيق (Deployment)

### 1. متطلبات الخادم
```
- Web Server (Apache/Nginx)
- HTTPS Certificate
- Gzip Compression
- Cache Headers
```

### 2. تحسين الإنتاج
```javascript
// تصغير الملفات
// ضغط الصور
// تجميع CSS/JS
// إعداد Service Worker للتخزين المؤقت
```

### 3. مراقبة الأداء
```javascript
// Google Analytics
// Error Tracking
// Performance Monitoring
// API Usage Tracking
```

## استكشاف الأخطاء التقنية

### 1. أخطاء JavaScript الشائعة
```javascript
// خطأ: Cannot read property of undefined
// الحل: التحقق من وجود الكائن قبل الوصول
if (object && object.property) { }

// خطأ: Promise rejection
// الحل: استخدام try/catch مع async/await
```

### 2. أخطاء CSS
```css
/* مشكلة: عناصر لا تظهر بشكل صحيح في RTL */
/* الحل: استخدام logical properties */
margin-inline-start: 1rem;
margin-inline-end: 1rem;
```

### 3. أخطاء API
```javascript
// مراقبة حالة الطلبات
console.log('API Stats:', apiManager.getUsageStats());

// تسجيل الأخطاء
console.error('API Error:', error.status, error.message);
```

## التحديثات المستقبلية

### 1. تحسينات مخططة
- إضافة Service Worker للعمل دون اتصال
- تحسين خوارزميات الذكاء الاصطناعي
- دعم المزيد من اللغات
- تطبيق جوال مخصص

### 2. ميزات جديدة
- نظام تقييم النتائج
- مشاركة النتائج عبر الروابط
- إحصائيات متقدمة للاستخدام
- تكامل مع قواعد بيانات خارجية

### 3. تحسينات الأداء
- تحميل تدريجي للمحتوى
- تخزين مؤقت ذكي
- ضغط البيانات
- تحسين استعلامات API

---

**ملاحظة للمطورين**: هذا المستند يتطور مع تطور المشروع. يُرجى مراجعة آخر التحديثات قبل البدء في التطوير.

**تاريخ آخر تحديث**: سبتمبر 2024  
**الإصدار التقني**: 1.0.0
