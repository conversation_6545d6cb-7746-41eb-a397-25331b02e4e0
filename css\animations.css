/* Animations CSS - مكتبة الإعجاز العلمي */

/* Loading Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.3);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(44, 90, 160, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(44, 90, 160, 0.6);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes typewriter {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    0%, 50% { border-color: transparent; }
    51%, 100% { border-color: var(--primary-color); }
}

/* Hover Animations */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(44, 90, 160, 0.4);
}

.hover-bounce {
    transition: transform 0.3s ease;
}

.hover-bounce:hover {
    animation: bounce 0.6s ease;
}

/* Loading States */
.loading-spinner {
    animation: spin 1s linear infinite;
}

.loading-pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

.loading-dots::after {
    content: '';
    animation: typewriter 1.5s steps(3, end) infinite;
}

.shimmer-effect {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Page Transitions */
.page-enter {
    animation: fadeInUp 0.6s ease-out;
}

.page-exit {
    animation: fadeInDown 0.6s ease-in;
}

.section-enter {
    animation: fadeInUp 0.8s ease-out;
}

.card-enter {
    animation: slideInUp 0.5s ease-out;
}

.modal-enter {
    animation: zoomIn 0.3s ease-out;
}

.modal-exit {
    animation: zoomOut 0.3s ease-in;
}

/* Floating Elements */
.floating-1 {
    animation: float 6s ease-in-out infinite;
}

.floating-2 {
    animation: float 6s ease-in-out infinite;
    animation-delay: 2s;
}

.floating-3 {
    animation: float 6s ease-in-out infinite;
    animation-delay: 4s;
}

/* Text Animations */
.text-glow {
    animation: glow 2s ease-in-out infinite alternate;
}

.text-typewriter {
    overflow: hidden;
    border-left: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typewriter 3s steps(40, end), blink 0.75s step-end infinite;
}

/* Button Animations */
.btn-animated {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-animated:hover::before {
    left: 100%;
}

.btn-pulse {
    animation: pulse 2s infinite;
}

.btn-bounce:hover {
    animation: bounce 0.6s ease;
}

/* Progress Animations */
.progress-animated .progress-bar {
    animation: slideInLeft 1s ease-out;
}

.progress-glow .progress-bar {
    box-shadow: 0 0 10px rgba(44, 90, 160, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

/* Card Animations */
.card-flip {
    perspective: 1000px;
}

.card-flip-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.card-flip:hover .card-flip-inner {
    transform: rotateY(180deg);
}

.card-flip-front,
.card-flip-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
}

.card-flip-back {
    transform: rotateY(180deg);
}

/* Scroll Animations */
.scroll-fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-slide-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-slide-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-slide-right {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-slide-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-zoom-in {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-zoom-in.visible {
    opacity: 1;
    transform: scale(1);
}

/* Search Animations */
.search-highlight {
    animation: glow 1s ease-in-out;
}

.search-result-enter {
    animation: fadeInUp 0.5s ease-out;
}

.search-loading {
    position: relative;
}

.search-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Notification Animations */
.notification-enter {
    animation: slideInDown 0.5s ease-out;
}

.notification-exit {
    animation: slideInUp 0.5s ease-in;
}

.notification-shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
    20%, 40%, 60%, 80% { transform: translateX(10px); }
}

/* Verse and Hadith Animations */
.verse-container,
.hadith-container {
    animation: fadeInUp 0.6s ease-out;
}

.verse-text,
.hadith-text {
    position: relative;
    overflow: hidden;
}

.verse-text::before,
.hadith-text::before {
    animation: fadeIn 0.8s ease-out 0.3s both;
}

.verse-text::after,
.hadith-text::after {
    animation: fadeIn 0.8s ease-out 0.5s both;
}

/* Table Animations */
.table-container {
    animation: fadeInUp 0.6s ease-out;
}

.table tbody tr {
    animation: fadeInLeft 0.5s ease-out;
}

.table tbody tr:nth-child(even) {
    animation-delay: 0.1s;
}

.table tbody tr:nth-child(odd) {
    animation-delay: 0.2s;
}

/* FAQ Animations */
.accordion-item {
    animation: fadeInUp 0.5s ease-out;
}

.accordion-item:nth-child(1) { animation-delay: 0.1s; }
.accordion-item:nth-child(2) { animation-delay: 0.2s; }
.accordion-item:nth-child(3) { animation-delay: 0.3s; }
.accordion-item:nth-child(4) { animation-delay: 0.4s; }
.accordion-item:nth-child(5) { animation-delay: 0.5s; }

.accordion-collapse {
    transition: all 0.3s ease;
}

/* Export Button Animations */
.results-actions .btn {
    animation: fadeInRight 0.5s ease-out;
}

.results-actions .btn:nth-child(1) { animation-delay: 0.1s; }
.results-actions .btn:nth-child(2) { animation-delay: 0.2s; }
.results-actions .btn:nth-child(3) { animation-delay: 0.3s; }
.results-actions .btn:nth-child(4) { animation-delay: 0.4s; }
.results-actions .btn:nth-child(5) { animation-delay: 0.5s; }

/* Responsive Animation Adjustments */
@media (max-width: 768px) {
    .floating-1,
    .floating-2,
    .floating-3 {
        animation: none;
    }
    
    .hover-lift:hover,
    .hover-scale:hover,
    .hover-rotate:hover {
        transform: none;
    }
    
    .card-flip:hover .card-flip-inner {
        transform: none;
    }
}

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .floating-1,
    .floating-2,
    .floating-3,
    .loading-spinner,
    .loading-pulse,
    .text-glow,
    .btn-pulse {
        animation: none;
    }
}

/* Print Animations (Disabled) */
@media print {
    *,
    *::before,
    *::after {
        animation: none !important;
        transition: none !important;
    }
}

/* Dark Mode Animations (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    .glow {
        animation: glow 2s ease-in-out infinite alternate;
        box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    }
    
    .shimmer-effect {
        background: linear-gradient(90deg, #333 25%, #555 50%, #333 75%);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
    }
}

/* Accessibility Animations */
.focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    animation: glow 0.3s ease-out;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    transition: top 0.3s ease;
}

.skip-link:focus {
    top: 6px;
    animation: fadeInDown 0.3s ease-out;
}

/* Performance Optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Animation Utilities */
.animate-delay-1 { animation-delay: 0.1s; }
.animate-delay-2 { animation-delay: 0.2s; }
.animate-delay-3 { animation-delay: 0.3s; }
.animate-delay-4 { animation-delay: 0.4s; }
.animate-delay-5 { animation-delay: 0.5s; }

.animate-duration-fast { animation-duration: 0.3s; }
.animate-duration-normal { animation-duration: 0.5s; }
.animate-duration-slow { animation-duration: 0.8s; }

.animate-ease-in { animation-timing-function: ease-in; }
.animate-ease-out { animation-timing-function: ease-out; }
.animate-ease-in-out { animation-timing-function: ease-in-out; }

.animate-infinite { animation-iteration-count: infinite; }
.animate-once { animation-iteration-count: 1; }
.animate-twice { animation-iteration-count: 2; }
