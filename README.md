# مكتبة الإعجاز العلمي - Scientific Miracles Library

أداة ذكية تستخدم الذكاء الاصطناعي لاستكشاف وتحليل الإعجاز العلمي في القرآن الكريم والسنة النبوية الشريفة.

## المميزات

- 🧠 **ذكاء اصطناعي متقدم**: يستخدم Gemini AI لتحليل المحتوى
- 📚 **قاعدة بيانات شاملة**: مصادر موثقة من القرآن والسنة
- 📊 **تحليل مفصل**: جداول ومقارنات علمية دقيقة
- 📱 **تصميم متجاوب**: يعمل على جميع الأجهزة
- 📄 **تصدير متعدد**: PDF, Excel, HTML, ZIP
- 🔍 **بحث متقدم**: في النتائج والمحتوى

## كيفية الاستخدام

### 1. تشغيل المشروع

```bash
# تشغيل خادم محلي
python -m http.server 8000

# أو باستخدام Node.js
npx http-server

# ثم افتح المتصفح على
http://localhost:8000
```

### 2. البحث

1. أدخل الموضوع العلمي (مثل: الفضاء، الطب، الجيولوجيا)
2. اختر الإعدادات المتقدمة (اختياري)
3. اضغط "ابحث"
4. انتظر النتائج وتصفحها

### 3. التصدير

- اختر صيغة التصدير المطلوبة
- سيتم تحميل الملف تلقائياً

## حل المشاكل الشائعة

### المشكلة: لا تظهر النتائج

**الأسباب المحتملة:**
1. مشكلة في مفاتيح API
2. ضعف الاتصال بالإنترنت
3. خطأ في معالجة البيانات

**الحلول:**
1. افتح صفحة الاختبار: `http://localhost:8000/test.html`
2. اضغط "اختبار جميع المفاتيح" للتحقق من صحة مفاتيح API
3. تحقق من وحدة تحكم المطور (F12) للأخطاء
4. جرب موضوع بحث مختلف

### المشكلة: خطأ في API

**الحلول:**
1. تحقق من الاتصال بالإنترنت
2. انتظر قليلاً ثم حاول مرة أخرى (قد يكون هناك حد للطلبات)
3. تحديث مفاتيح API في `js/config.js`

### المشكلة: المحتوى غير مُنسق بشكل صحيح

**الحلول:**
1. تحديث الصفحة
2. مسح ذاكرة التخزين المؤقت للمتصفح
3. تحقق من وحدة تحكم المطور للأخطاء

## الملفات الرئيسية

```
├── index.html              # الصفحة الرئيسية
├── test.html              # صفحة الاختبار والتشخيص
├── css/
│   ├── main.css           # الأنماط الرئيسية
│   ├── components.css     # أنماط المكونات
│   └── animations.css     # الحركات والتأثيرات
├── js/
│   ├── config.js          # الإعدادات ومفاتيح API
│   ├── main.js            # الملف الرئيسي
│   ├── ai-integration.js  # تكامل الذكاء الاصطناعي
│   ├── content-generator.js # مولد المحتوى
│   └── export-manager.js  # إدارة التصدير
└── README.md              # هذا الملف
```

## إعدادات API

يمكن تحديث مفاتيح Gemini API في ملف `js/config.js`:

```javascript
const GEMINI_API_KEYS = [
    'your-api-key-1',
    'your-api-key-2',
    // أضف المزيد من المفاتيح
];
```

## الاختبار والتشخيص

استخدم صفحة الاختبار `test.html` لـ:
- اختبار الوظائف الأساسية
- فحص مفاتيح API
- تشخيص المشاكل
- مراقبة سجل الأحداث

## المتطلبات

- متصفح حديث يدعم ES6+
- اتصال بالإنترنت
- مفاتيح Gemini API صالحة

## الدعم الفني

إذا واجهت مشاكل:
1. تحقق من صفحة الاختبار أولاً
2. راجع وحدة تحكم المطور (F12)
3. تأكد من صحة مفاتيح API
4. تحقق من الاتصال بالإنترنت

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والبحثي.

---

**ملاحظة:** تأكد من استخدام مفاتيح API صالحة ومن وجود اتصال مستقر بالإنترنت للحصول على أفضل النتائج.
