<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مكتبة الإعجاز العلمي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="test-container">
            <h1 class="text-center mb-4">
                <i class="fas fa-vial text-primary"></i>
                اختبار مكتبة الإعجاز العلمي
            </h1>

            <div class="test-section">
                <h3><i class="fas fa-play-circle text-success"></i> اختبار سريع</h3>
                <p>اختبر الوظائف الأساسية للمكتبة</p>
                <button id="testBasicBtn" class="btn btn-primary">
                    <i class="fas fa-test-tube"></i>
                    اختبار الوظائف الأساسية
                </button>
                <button id="testAPIBtn" class="btn btn-warning">
                    <i class="fas fa-cloud"></i>
                    اختبار API
                </button>
                <button id="testAllKeysBtn" class="btn btn-danger">
                    <i class="fas fa-key"></i>
                    اختبار جميع المفاتيح
                </button>
                <button id="testContentBtn" class="btn btn-info">
                    <i class="fas fa-file-text"></i>
                    اختبار تنسيق المحتوى
                </button>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-search text-info"></i> اختبار البحث</h3>
                <div class="input-group mb-3">
                    <input type="text" id="testSearchInput" class="form-control" 
                           placeholder="أدخل موضوع للاختبار (مثل: الفضاء)" value="الفضاء">
                    <button id="testSearchBtn" class="btn btn-success">
                        <i class="fas fa-search"></i>
                        اختبار البحث
                    </button>
                </div>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-terminal text-secondary"></i> سجل الأحداث</h3>
                <div id="logOutput" class="log-output"></div>
                <button id="clearLogBtn" class="btn btn-sm btn-outline-secondary mt-2">
                    <i class="fas fa-trash"></i>
                    مسح السجل
                </button>
                <button id="showErrorLogBtn" class="btn btn-sm btn-outline-danger mt-2 me-2">
                    <i class="fas fa-bug"></i>
                    سجل الأخطاء
                </button>
                <button id="exportErrorReportBtn" class="btn btn-sm btn-outline-warning mt-2 me-2">
                    <i class="fas fa-download"></i>
                    تصدير تقرير الأخطاء
                </button>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-chart-bar text-warning"></i> النتائج</h3>
                <div id="testResults" class="alert alert-info">
                    لم يتم تشغيل أي اختبارات بعد
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/error-handler.js"></script>
    <script src="js/ai-integration.js"></script>
    <script src="js/content-generator.js"></script>
    <script src="js/export-manager.js"></script>

    <script>
        // إعداد السجل
        const logOutput = document.getElementById('logOutput');
        const testResults = document.getElementById('testResults');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logOutput.textContent += logEntry;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }

        function showResult(message, type = 'info') {
            testResults.className = `alert alert-${type}`;
            testResults.textContent = message;
        }

        // اختبار الوظائف الأساسية
        document.getElementById('testBasicBtn').addEventListener('click', function() {
            log('بدء اختبار الوظائف الأساسية');
            
            try {
                // اختبار Utils
                const testId = Utils.generateId();
                log(`تم إنشاء معرف: ${testId}`);
                
                const testDate = Utils.formatDate();
                log(`تاريخ مُنسق: ${testDate}`);
                
                // اختبار ContentGenerator
                const testContent = {
                    content: '# اختبار\n\n## قسم تجريبي\n\nهذا محتوى تجريبي للاختبار.',
                    topic: 'اختبار',
                    timestamp: testDate,
                    searchId: testId
                };
                
                const formatted = contentGenerator.formatContent(testContent);
                log(`تم تنسيق المحتوى بنجاح (${formatted.length} حرف)`);
                
                showResult('تم اجتياز جميع الاختبارات الأساسية بنجاح!', 'success');
                
            } catch (error) {
                log(`خطأ في الاختبار: ${error.message}`, 'error');
                showResult(`فشل في الاختبار: ${error.message}`, 'danger');
            }
        });

        // اختبار API
        document.getElementById('testAPIBtn').addEventListener('click', async function() {
            log('بدء اختبار API');

            try {
                const stats = apiManager.getUsageStats();
                log(`إحصائيات API: ${stats.availableKeys} مفاتيح متاحة من أصل ${stats.totalKeys}`);

                const testPrompt = 'اكتب جملة واحدة عن الإعجاز العلمي في القرآن';
                log('إرسال طلب تجريبي إلى API...');

                const result = await aiIntegration.sendGeminiRequest(testPrompt);
                log(`تم استلام الرد: ${result.substring(0, 100)}...`);

                showResult('تم اختبار API بنجاح!', 'success');

            } catch (error) {
                log(`خطأ في اختبار API: ${error.message}`, 'error');
                showResult(`فشل اختبار API: ${error.message}`, 'danger');
            }
        });

        // اختبار جميع مفاتيح API
        document.getElementById('testAllKeysBtn').addEventListener('click', async function() {
            log('بدء اختبار جميع مفاتيح API...');
            showResult('جاري اختبار المفاتيح...', 'info');

            try {
                const results = await apiManager.testAllKeys();

                let validCount = 0;
                results.forEach((result, index) => {
                    const status = result.valid ? 'صالح' : 'غير صالح';
                    log(`المفتاح ${index + 1}: ${result.key} - ${status}`);
                    if (result.valid) validCount++;
                });

                log(`النتيجة النهائية: ${validCount}/${results.length} مفاتيح صالحة`);

                if (validCount > 0) {
                    showResult(`تم العثور على ${validCount} مفاتيح صالحة من أصل ${results.length}`, 'success');
                } else {
                    showResult('لا توجد مفاتيح API صالحة!', 'danger');
                }

            } catch (error) {
                log(`خطأ في اختبار المفاتيح: ${error.message}`, 'error');
                showResult(`فشل اختبار المفاتيح: ${error.message}`, 'danger');
            }
        });

        // اختبار تنسيق المحتوى
        document.getElementById('testContentBtn').addEventListener('click', function() {
            log('بدء اختبار تنسيق المحتوى');
            
            try {
                const testMarkdown = `# العنوان الرئيسي

## القسم الأول
هذا نص تجريبي **عريض** و *مائل*.

## القسم الثاني
- نقطة أولى
- نقطة ثانية
- نقطة ثالثة

> هذا اقتباس تجريبي

### قسم فرعي
نص إضافي للاختبار.`;

                const html = contentGenerator.parseMarkdownToHTML(testMarkdown);
                log(`تم تحويل Markdown إلى HTML (${html.length} حرف)`);
                
                const testContent = {
                    content: testMarkdown,
                    topic: 'اختبار التنسيق',
                    timestamp: Utils.formatDate(),
                    searchId: Utils.generateId()
                };
                
                const formatted = contentGenerator.formatContent(testContent);
                log(`تم تنسيق المحتوى الكامل (${formatted.length} حرف)`);
                
                showResult('تم اختبار تنسيق المحتوى بنجاح!', 'success');
                
            } catch (error) {
                log(`خطأ في اختبار التنسيق: ${error.message}`, 'error');
                showResult(`فشل اختبار التنسيق: ${error.message}`, 'danger');
            }
        });

        // اختبار البحث
        document.getElementById('testSearchBtn').addEventListener('click', async function() {
            const topic = document.getElementById('testSearchInput').value.trim();
            
            if (!topic) {
                showResult('يرجى إدخال موضوع للاختبار', 'warning');
                return;
            }
            
            log(`بدء اختبار البحث للموضوع: ${topic}`);
            
            try {
                const settings = {
                    detailLevel: 'basic',
                    sourceType: 'all',
                    includeTables: false,
                    includeFAQ: false
                };
                
                log('إعدادات البحث: ' + JSON.stringify(settings));
                
                const results = await aiIntegration.processLargeContent(topic, settings);
                log(`تم الحصول على النتائج: ${results.wordCount} كلمة، ${results.sections} قسم`);
                
                showResult(`تم اختبار البحث بنجاح! تم إنتاج ${results.wordCount} كلمة في ${results.sections} قسم`, 'success');
                
            } catch (error) {
                log(`خطأ في اختبار البحث: ${error.message}`, 'error');
                showResult(`فشل اختبار البحث: ${error.message}`, 'danger');
            }
        });

        // مسح السجل
        document.getElementById('clearLogBtn').addEventListener('click', function() {
            logOutput.textContent = '';
            log('تم مسح السجل');
        });

        // عرض سجل الأخطاء
        document.getElementById('showErrorLogBtn').addEventListener('click', function() {
            if (window.errorHandler) {
                const errorLog = errorHandler.getErrorLog();
                log(`سجل الأخطاء يحتوي على ${errorLog.length} خطأ`);

                if (errorLog.length === 0) {
                    log('لا توجد أخطاء مسجلة');
                    showResult('لا توجد أخطاء مسجلة', 'success');
                } else {
                    errorLog.slice(0, 5).forEach((error, index) => {
                        log(`خطأ ${index + 1}: ${error.type} - ${error.message || error.userMessage}`);
                    });
                    showResult(`تم عرض آخر ${Math.min(5, errorLog.length)} أخطاء في السجل`, 'info');
                }
            } else {
                log('معالج الأخطاء غير متاح', 'error');
                showResult('معالج الأخطاء غير متاح', 'danger');
            }
        });

        // تصدير تقرير الأخطاء
        document.getElementById('exportErrorReportBtn').addEventListener('click', function() {
            if (window.errorHandler) {
                try {
                    errorHandler.exportErrorReport();
                    log('تم تصدير تقرير الأخطاء');
                    showResult('تم تصدير تقرير الأخطاء بنجاح', 'success');
                } catch (error) {
                    log(`خطأ في تصدير التقرير: ${error.message}`, 'error');
                    showResult('فشل في تصدير التقرير', 'danger');
                }
            } else {
                log('معالج الأخطاء غير متاح', 'error');
                showResult('معالج الأخطاء غير متاح', 'danger');
            }
        });

        // رسالة ترحيب
        log('تم تحميل صفحة الاختبار بنجاح');
        log('جاهز لبدء الاختبارات');

        // تحقق من معالج الأخطاء
        if (window.errorHandler) {
            log('معالج الأخطاء متاح ويعمل');
        } else {
            log('تحذير: معالج الأخطاء غير متاح', 'warning');
        }
    </script>
</body>
</html>
