// Error Handler Module - مكتبة الإعجاز العلمي

class ErrorHandler {
    constructor() {
        this.errorLog = [];
        this.maxLogSize = 100;
        this.setupGlobalErrorHandling();
    }

    // إعداد معالجة الأخطاء العامة
    setupGlobalErrorHandling() {
        // معالجة الأخطاء غير المتوقعة
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                line: event.lineno,
                column: event.colno,
                stack: event.error?.stack
            });
        });

        // معالجة الأخطاء في الوعود
        window.addEventListener('unhandledrejection', (event) => {
            this.logError({
                type: 'Unhandled Promise Rejection',
                message: event.reason?.message || event.reason,
                stack: event.reason?.stack
            });
        });
    }

    // تسجيل الأخطاء
    logError(error) {
        const errorEntry = {
            timestamp: new Date().toISOString(),
            ...error
        };

        this.errorLog.unshift(errorEntry);

        // الحفاظ على حجم السجل
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog = this.errorLog.slice(0, this.maxLogSize);
        }

        // طباعة الخطأ في وحدة التحكم
        console.error('خطأ مسجل:', errorEntry);

        // حفظ في التخزين المحلي
        this.saveErrorLog();
    }

    // حفظ سجل الأخطاء
    saveErrorLog() {
        try {
            localStorage.setItem('scientificMiraclesErrorLog', JSON.stringify(this.errorLog));
        } catch (error) {
            console.error('فشل في حفظ سجل الأخطاء:', error);
        }
    }

    // تحميل سجل الأخطاء
    loadErrorLog() {
        try {
            const saved = localStorage.getItem('scientificMiraclesErrorLog');
            if (saved) {
                this.errorLog = JSON.parse(saved);
            }
        } catch (error) {
            console.error('فشل في تحميل سجل الأخطاء:', error);
            this.errorLog = [];
        }
    }

    // الحصول على سجل الأخطاء
    getErrorLog() {
        return this.errorLog;
    }

    // مسح سجل الأخطاء
    clearErrorLog() {
        this.errorLog = [];
        try {
            localStorage.removeItem('scientificMiraclesErrorLog');
        } catch (error) {
            console.error('فشل في مسح سجل الأخطاء:', error);
        }
    }

    // معالجة أخطاء API
    handleAPIError(error, context = '') {
        let userMessage = 'حدث خطأ في الخدمة';
        let technicalMessage = error.message;
        let suggestions = [];

        // تحليل نوع الخطأ
        if (error.message.includes('403') || error.message.includes('API key')) {
            userMessage = 'مفتاح API غير صالح أو منتهي الصلاحية';
            suggestions = [
                'تحقق من صحة مفاتيح API',
                'تأكد من تفعيل Gemini API في Google Cloud Console',
                'راجع حدود الاستخدام لمفاتيح API'
            ];
        } else if (error.message.includes('429')) {
            userMessage = 'تم تجاوز حد الطلبات المسموح به';
            suggestions = [
                'انتظر قليلاً ثم حاول مرة أخرى',
                'قلل من عدد الطلبات المتزامنة',
                'استخدم مفاتيح API إضافية'
            ];
        } else if (error.message.includes('400')) {
            userMessage = 'خطأ في تنسيق الطلب';
            suggestions = [
                'تحقق من صحة البيانات المرسلة',
                'جرب موضوع بحث مختلف',
                'تأكد من أن النص ليس طويلاً جداً'
            ];
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
            userMessage = 'خطأ في الاتصال بالإنترنت';
            suggestions = [
                'تحقق من اتصالك بالإنترنت',
                'جرب إعادة تحميل الصفحة',
                'تأكد من عدم حجب الموقع من قبل جدار الحماية'
            ];
        }

        // تسجيل الخطأ
        this.logError({
            type: 'API Error',
            context: context,
            userMessage: userMessage,
            technicalMessage: technicalMessage,
            suggestions: suggestions
        });

        return {
            userMessage,
            technicalMessage,
            suggestions
        };
    }

    // معالجة أخطاء المحتوى
    handleContentError(error, content = null) {
        let userMessage = 'خطأ في معالجة المحتوى';
        let suggestions = [];

        if (error.message.includes('formatContent')) {
            userMessage = 'خطأ في تنسيق المحتوى';
            suggestions = [
                'جرب إعادة البحث',
                'تحقق من صحة البيانات',
                'راجع إعدادات التنسيق'
            ];
        } else if (error.message.includes('parseMarkdown')) {
            userMessage = 'خطأ في تحليل النص';
            suggestions = [
                'تحقق من تنسيق النص',
                'جرب محتوى أبسط',
                'راجع قواعد Markdown'
            ];
        }

        this.logError({
            type: 'Content Error',
            userMessage: userMessage,
            technicalMessage: error.message,
            suggestions: suggestions,
            contentLength: content?.length || 0
        });

        return {
            userMessage,
            suggestions
        };
    }

    // إنشاء تقرير الأخطاء
    generateErrorReport() {
        const report = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            errorCount: this.errorLog.length,
            recentErrors: this.errorLog.slice(0, 10),
            systemInfo: {
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine
            }
        };

        return report;
    }

    // تصدير تقرير الأخطاء
    exportErrorReport() {
        const report = this.generateErrorReport();
        const blob = new Blob([JSON.stringify(report, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `error-report-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // عرض رسالة خطأ للمستخدم
    showUserFriendlyError(error, context = '') {
        const errorInfo = this.handleAPIError(error, context);
        
        let alertHTML = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <h5><i class="fas fa-exclamation-triangle"></i> ${errorInfo.userMessage}</h5>
                <p><strong>السبب التقني:</strong> ${errorInfo.technicalMessage}</p>
        `;

        if (errorInfo.suggestions.length > 0) {
            alertHTML += '<p><strong>الحلول المقترحة:</strong></p><ul>';
            errorInfo.suggestions.forEach(suggestion => {
                alertHTML += `<li>${suggestion}</li>`;
            });
            alertHTML += '</ul>';
        }

        alertHTML += `
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إضافة التنبيه إلى الصفحة
        const alertContainer = document.createElement('div');
        alertContainer.innerHTML = alertHTML;
        alertContainer.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 10000; max-width: 400px;';
        document.body.appendChild(alertContainer);

        // إزالة تلقائية بعد 10 ثوان
        setTimeout(() => {
            if (alertContainer.parentNode) {
                alertContainer.remove();
            }
        }, 10000);
    }
}

// إنشاء مثيل من معالج الأخطاء
const errorHandler = new ErrorHandler();

// تحميل سجل الأخطاء السابق
errorHandler.loadErrorLog();

// تصدير للاستخدام في الملفات الأخرى
window.ErrorHandler = ErrorHandler;
window.errorHandler = errorHandler;

console.log('تم تحميل وحدة معالجة الأخطاء بنجاح');
